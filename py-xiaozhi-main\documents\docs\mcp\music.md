# 音乐播放工具 (Music Tools)

音乐播放工具是一个功能丰富的 MCP 音乐播放器，支持在线搜索播放、本地音乐管理、歌词显示等功能。

### 常见使用场景

**搜索播放在线音乐:**
- "播放周杰伦的青花瓷"
- "我想听邓紫棋的歌"
- "放一首轻音乐"
- "播放最新的流行歌曲"

**播放控制:**
- "暂停音乐"
- "继续播放"
- "停止播放"
- "音乐播放到一半了"

**本地音乐管理:**
- "查看本地音乐"
- "播放本地的那首歌"
- "在本地音乐中搜索周杰伦"

**播放状态查询:**
- "现在播放的是什么歌"
- "播放进度怎么样"
- "这首歌还有多长时间"

**歌词功能:**
- "显示歌词"
- "当前歌词是什么"
- "有歌词吗"

**高级功能:**
- "跳转到1分30秒"
- "快进到高潮部分"
- "回到开头"

### 使用提示

1. **明确歌曲信息**: 提供歌名、歌手名或专辑名有助于更准确的搜索
2. **网络连接**: 在线搜索和播放需要稳定的网络连接
3. **本地缓存**: 播放过的歌曲会自动缓存，下次播放更快
4. **音量控制**: 可以要求调整音量或静音
5. **歌词同步**: 支持实时歌词显示，增强听歌体验

AI 助手会根据您的需求自动调用音乐播放工具，为您提供流畅的音乐体验。

## 功能概览

### 在线音乐功能
- **智能搜索**: 支持歌名、歌手、专辑等多种搜索方式
- **高品质播放**: 支持高品质音频流播放
- **歌词显示**: 实时同步歌词显示
- **自动缓存**: 播放过的歌曲自动缓存到本地

### 本地音乐管理
- **本地扫描**: 自动扫描本地音乐文件
- **元数据提取**: 自动提取歌曲标题、艺术家、专辑等信息
- **格式支持**: 支持MP3、M4A、FLAC、WAV、OGG等多种格式
- **智能搜索**: 在本地音乐中快速搜索

### 播放控制功能
- **基础控制**: 播放、暂停、停止
- **进度控制**: 跳转到指定时间位置
- **状态查询**: 获取播放状态、进度等信息
- **错误处理**: 完善的错误处理和恢复机制

### 用户体验功能
- **UI集成**: 与应用界面无缝集成
- **实时反馈**: 实时显示播放状态和歌词
- **智能缓存**: 优化存储空间使用
- **后台播放**: 支持后台持续播放

## 工具列表

### 1. 在线音乐工具

#### search_and_play - 搜索并播放
搜索在线音乐并开始播放。

**参数:**
- `song_name` (必需): 要搜索的歌曲名称、歌手或关键词

**使用场景:**
- 播放指定歌曲
- 搜索歌手的歌曲
- 播放流行音乐

### 2. 本地音乐工具

#### get_local_playlist - 获取本地音乐列表
获取本地缓存的音乐文件列表。

**参数:**
- `force_refresh` (可选): 是否强制刷新，默认false

**使用场景:**
- 查看本地音乐
- 管理音乐库
- 选择播放列表

#### search_local_music - 搜索本地音乐
在本地音乐中搜索指定歌曲。

**参数:**
- `query` (必需): 搜索关键词

**使用场景:**
- 查找本地歌曲
- 艺术家搜索
- 专辑搜索

#### play_local_song_by_id - 播放本地歌曲
根据歌曲ID播放本地音乐。

**参数:**
- `file_id` (必需): 本地音乐文件ID

**使用场景:**
- 播放指定本地歌曲
- 从播放列表选择
- 快速播放缓存音乐

### 3. 播放控制工具

#### play_pause - 播放/暂停切换
切换播放和暂停状态。

**参数:**
无

**使用场景:**
- 暂停当前播放
- 恢复播放
- 播放控制

#### stop - 停止播放
停止当前播放。

**参数:**
无

**使用场景:**
- 完全停止播放
- 结束音乐会话
- 清除播放状态

#### seek - 跳转到指定位置
跳转到歌曲的指定时间位置。

**参数:**
- `position` (必需): 跳转位置（秒）

**使用场景:**
- 快进到高潮部分
- 重复播放某段
- 跳过不喜欢的部分

### 4. 信息查询工具

#### get_status - 获取播放状态
获取当前播放器的详细状态信息。

**参数:**
无

**使用场景:**
- 查看播放进度
- 检查播放状态
- 获取歌曲信息

#### get_lyrics - 获取歌词
获取当前播放歌曲的歌词。

**参数:**
无

**使用场景:**
- 显示歌词
- 跟唱歌曲
- 学习歌词

## 使用示例

### 在线音乐播放示例

```python
# 搜索并播放歌曲
result = await mcp_server.call_tool("search_and_play", {
    "song_name": "周杰伦 青花瓷"
})

# 播放/暂停控制
result = await mcp_server.call_tool("play_pause", {})

# 停止播放
result = await mcp_server.call_tool("stop", {})

# 跳转到指定位置
result = await mcp_server.call_tool("seek", {
    "position": 90.5
})
```

### 本地音乐管理示例

```python
# 获取本地音乐列表
result = await mcp_server.call_tool("get_local_playlist", {
    "force_refresh": True
})

# 搜索本地音乐
result = await mcp_server.call_tool("search_local_music", {
    "query": "周杰伦"
})

# 播放本地歌曲
result = await mcp_server.call_tool("play_local_song_by_id", {
    "file_id": "song_123"
})
```

### 状态查询示例

```python
# 获取播放状态
result = await mcp_server.call_tool("get_status", {})

# 获取歌词
result = await mcp_server.call_tool("get_lyrics", {})
```

## 技术架构

### 音乐播放器核心
- **单例模式**: 全局唯一的播放器实例
- **异步设计**: 支持异步操作，不阻塞主线程
- **状态管理**: 完善的播放状态管理
- **错误处理**: 健壮的错误处理机制

### 音频处理
- **Pygame集成**: 使用Pygame Mixer进行音频播放
- **格式支持**: 支持多种音频格式
- **缓存机制**: 智能缓存策略，减少重复下载
- **音质优化**: 高品质音频播放

### 在线服务集成
- **API接口**: 集成在线音乐搜索API
- **下载管理**: 异步下载和缓存管理
- **歌词服务**: 实时歌词获取和显示
- **网络优化**: 网络请求优化和重试机制

### 本地音乐管理
- **文件扫描**: 自动扫描本地音乐文件
- **元数据提取**: 使用Mutagen库提取音乐元数据
- **索引建立**: 建立音乐文件索引，提高搜索效率
- **格式识别**: 智能识别音乐文件格式

## 数据结构

### 播放状态信息
```python
{
    "status": "success",
    "current_song": "青花瓷 - 周杰伦",
    "is_playing": true,
    "paused": false,
    "duration": 237.5,
    "position": 89.2,
    "progress": 37.6,
    "has_lyrics": true
}
```

### 音乐元数据
```python
{
    "file_id": "song_123",
    "title": "青花瓷",
    "artist": "周杰伦",
    "album": "我很忙",
    "duration": "03:57",
    "file_size": 5242880,
    "format": "mp3"
}
```

### 歌词数据
```python
{
    "status": "success",
    "lyrics": [
        "[00:12] 素胚勾勒出青花笔锋浓转淡",
        "[00:18] 瓶身描绘的牡丹一如你初妆",
        "[00:24] 冉冉檀香透过窗心事我了然"
    ]
}
```

## 配置说明

### 音频配置
音频播放相关配置：
```python
AudioConfig = {
    "OUTPUT_SAMPLE_RATE": 44100,
    "CHANNELS": 2,
    "BUFFER_SIZE": 1024
}
```

### 缓存配置
缓存目录配置：
```python
cache_dir = Path(project_root) / "cache" / "music"
temp_cache_dir = cache_dir / "temp"
```

### API配置
在线音乐服务配置：
```python
config = {
    "SEARCH_URL": "http://search.kuwo.cn/r.s",
    "PLAY_URL": "http://api.xiaodaokg.com/kuwo.php",
    "LYRIC_URL": "http://m.kuwo.cn/newh5/singles/songinfoandlrc"
}
```

## 支持的音频格式

### 播放格式
- **MP3**: 最常见的音频格式
- **M4A**: Apple音频格式
- **FLAC**: 无损音频格式
- **WAV**: 未压缩音频格式
- **OGG**: 开源音频格式

### 元数据支持
- **ID3 v1/v2**: MP3元数据标准
- **MP4**: M4A文件元数据
- **Vorbis**: OGG文件元数据
- **FLAC**: FLAC文件元数据

## 最佳实践

### 1. 搜索优化
- 使用具体的歌名和歌手名
- 避免使用过于模糊的关键词
- 可以包含专辑名增加准确性

### 2. 缓存管理
- 定期清理不需要的缓存文件
- 监控缓存目录大小
- 使用强制刷新获取最新音乐列表

### 3. 网络优化
- 确保网络连接稳定
- 在网络不佳时优先使用本地音乐
- 设置合适的超时时间

### 4. 用户体验
- 提供清晰的播放状态反馈
- 支持快速响应的控制操作
- 优雅处理播放错误

## 故障排除

### 常见问题
1. **无法搜索歌曲**: 检查网络连接和API可用性
2. **播放失败**: 检查音频设备和文件格式
3. **歌词不显示**: 检查歌词服务和歌曲ID
4. **本地音乐不显示**: 检查文件权限和格式支持

### 调试方法
1. 查看日志输出获取详细错误信息
2. 测试网络连接和API响应
3. 验证音频文件完整性
4. 检查缓存目录权限

### 性能优化
1. 合理设置缓存策略
2. 优化网络请求频率
3. 使用异步操作避免阻塞
4. 定期清理临时文件

通过音乐播放工具，您可以享受丰富的音乐体验，包括在线搜索、本地播放、歌词显示等功能。

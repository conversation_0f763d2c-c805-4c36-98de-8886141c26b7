<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>opusinfo man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
P.level0 {
 padding-left: 2em;
}

P.level1 {
 padding-left: 4em;
}

P.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>

<p class="level0">
<p class="level0"><a name="NAME"></a><h2 class="nroffsh">NAME</h2>
<p class="level0">opusinfo - gives information about Opus files and does extensive validity checking 
<p class="level0"><a name="SYNOPSIS"></a><h2 class="nroffsh">SYNOPSIS</h2>
<p class="level0"><span Class="bold">opusinfo</span> [ <a class="bold" href="#-q">-q</a> | <a class="bold" href="#-v">-v</a> ] [ <a class="bold" href="#-h">-h</a> ] [ <a class="bold" href="#-V">-V</a> ] <span Class="emphasis">file1.opus</span> <span Class="bold">...</span> <span Class="emphasis">fileN.opus</span> 
<p class="level0"><a name="DESCRIPTION"></a><h2 class="nroffsh">DESCRIPTION</h2>
<p class="level0"><span Class="bold">opusinfo</span> reads one or more Opus files and prints information about stream contents (including chained and/or multiplexed streams) to standard output. It will detect (but not correct) a wide range of common defects, with many additional checks specifically for Opus streams. 
<p class="level0">For all stream types <span Class="bold">opusinfo</span> will print the filename being processed, the stream serial numbers, and various common error conditions. 
<p class="level0">For <span Class="bold">Opus</span> streams, information including the version used for encoding, number of channels and other header information, the bitrate and playback length, the contents of the comment header, and general statistics about the stream are printed. 
<p class="level0">Opusinfo is a fork of <span Class="bold">ogginfo</span>(1) with the non-opus parts largely removed. 
<p class="level0"><a name="OPTIONS"></a><h2 class="nroffsh">OPTIONS</h2>
<p class="level0">
<p class="level0"><a name="-h"></a><span class="nroffip">-h</span> 
<p class="level1">Show a help and usage message. 
<p class="level0"><a name="-q"></a><span class="nroffip">-q</span> 
<p class="level1">Quiet mode. This may be specified multiple times. Doing so once will remove the detailed informative messages; twice will remove warnings as well. 
<p class="level0"><a name="-v"></a><span class="nroffip">-v</span> 
<p class="level1">Verbose mode. At the current time, this does not do anything. 
<p class="level0"><a name="-V"></a><span class="nroffip">-V</span> 
<p class="level1">Show program version info and exit. 
<p class="level1"><a name="NOTES"></a><h2 class="nroffsh">NOTES</h2>
<p class="level0">
<p class="level0">There are many kinds of errored, invalid, non-normative, or otherwise unwise stream constructions which opusinfo will not produce warnings on. Passing opusinfo with flying colors is not certification of the correctness of a stream. Future versions may detect more error conditions. 
<p class="level0"><a name="AUTHORS"></a><h2 class="nroffsh">AUTHORS</h2>
<p class="level0">
<p class="level0">Michael Smith &lt;<EMAIL>&gt; 
<p class="level0">Gregory Maxwell &lt;<EMAIL>&gt; 
<p class="level0"><a name="SEE"></a><h2 class="nroffsh">SEE ALSO</h2>
<p class="level0">
<p class="level0"><a class="manpage" href="./opusdec.html">opusdec (1)</a> <a class="manpage" href="./opusenc.html">opusenc (1)</a> <p class="roffit">
 This HTML page was made with <a href="http://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>

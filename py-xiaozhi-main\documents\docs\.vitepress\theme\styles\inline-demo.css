.vt-doc a[href^="https://play.vuejs.org"]:before
{
  content: '▶';
  width: 20px;
  height: 20px;
  display: inline-flex;
  border-radius: 10px;
  vertical-align: middle;
  position: relative;
  top: -2px;
  color: var(--vt-c-green);
  border: 2px solid var(--vt-c-green);
  margin-right: 8px;
  margin-left: 4px;
  line-height: 16px;
  padding-left: 4.2px;
  font-size: 11px;
}

.demo {
  padding: 22px 24px;
  border-radius: 8px;
  box-shadow: var(--vt-shadow-2);
  margin-bottom: 1.2em;
  transition: background-color 0.5s ease;
}

.dark .demo {
  background-color: var(--vt-c-bg-soft);
}

.demo p {
  margin: 0;
}

.demo button {
  background-color: var(--vt-c-bg-mute);
  transition: background-color 0.5s;
  padding: 5px 12px;
  border: 1px solid var(--vt-c-divider);
  border-radius: 8px;
  font-size: 0.9em;
  font-weight: 600;
}

.demo button + button {
  margin-left: 1em;
}

.demo input,
.demo textarea,
.demo select {
  border: 1px solid var(--vt-c-divider);
  border-radius: 4px;
  padding: 0.2em 0.6em;
  margin-top: 10px;
  background: transparent;
  transition: background-color 0.5s;
}

.dark .demo select {
  background: var(--vt-c-bg-soft);
}

.dark .demo select option {
  background: transparent;
}

.demo input:not([type]):focus,
.demo textarea:focus,
.demo select:focus {
  outline: 1px solid blue;
}

.demo select {
  /* this was set by normalize.css */
  -webkit-appearance: listbox;
}

.demo label {
  margin: 0 1em 0 0.4em;
}

.demo select[multiple] {
  width: 100px;
}

.demo h1 {
  margin: 10px 0 0;
}

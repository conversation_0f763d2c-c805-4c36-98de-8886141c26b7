# 网络控制台使用指南

## 概述

网络控制台允许你通过串口终端直接发送命令来控制设备的网络连接，支持在WiFi和4G网络之间切换，以及查看网络状态。

## 可用命令

### 基本网络命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `4g` | 切换到4G网络 | `xiaozhi> 4g` |
| `wifi` | 切换到WiFi网络 | `xiaozhi> wifi` |
| `status` | 显示当前网络状态 | `xiaozhi> status` |
| `nethelp` | 显示帮助信息 | `xiaozhi> nethelp` |
| `reboot` | 重启设备 | `xiaozhi> reboot` |

### 使用方法

1. **打开串口终端**
   - 波特率：115200
   - 数据位：8
   - 停止位：1
   - 校验位：无

2. **连接设备**
   - 通过USB连接设备
   - 打开串口监视器（如Arduino IDE、PlatformIO、putty等）

3. **发送命令**
   - 在终端中输入命令并按回车
   - 命令提示符：`xiaozhi>`

## 命令详解

### 切换到4G网络
```
xiaozhi> 4g
Switching to 4G network (ML307)...
Saving network type to settings...
Network type set to 4G. Rebooting...
```

### 切换到WiFi网络
```
xiaozhi> wifi
Switching to WiFi network...
Saving network type to settings...
Network type set to WiFi. Rebooting...
```

### 查看网络状态
```
xiaozhi> status

=== Network Status ===
Current Network: 4G (ML307)
Network Icon: 📶

=== Device Info ===
WiFi MAC: 24:6F:28:XX:XX:XX
Chip: ESP32-S3 Rev 0
Firmware: 1.7.5
Build Date: Jan 25 2025 14:30:00
==================
```

### 获取帮助
```
xiaozhi> nethelp

=== Network Console Commands ===
4g        - Switch to 4G network (ML307)
wifi      - Switch to WiFi network
status    - Show current network status
nethelp   - Show this help message
reboot    - Reboot the device

Usage examples:
  xiaozhi> 4g       # Switch to 4G
  xiaozhi> wifi     # Switch to WiFi
  xiaozhi> status   # Check network status
================================
```

## 注意事项

1. **网络切换**：切换网络类型后设备会自动重启
2. **4G要求**：使用4G网络需要插入有效的SIM卡
3. **WiFi配置**：切换到WiFi后，如果没有保存的WiFi配置，设备会进入WiFi配置模式
4. **兼容性**：只有支持双网络的设备才能使用网络切换功能

## 故障排除

### 命令无响应
- 检查串口连接是否正常
- 确认波特率设置为115200
- 尝试发送回车键唤醒终端

### 切换失败
- 检查设备是否支持双网络模式
- 确认SIM卡已正确插入（4G模式）
- 查看串口日志获取详细错误信息

### 网络连接问题
- 4G：检查SIM卡状态和信号覆盖
- WiFi：检查WiFi配置和信号强度
- 使用`status`命令查看详细状态信息

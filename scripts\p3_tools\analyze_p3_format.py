#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P3格式分析工具
检查p3文件是否符合格式规范
"""

import struct
import os
import sys

def analyze_p3_file(file_path):
    """
    分析p3文件格式
    """
    print(f"分析文件: {file_path}")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} 字节")
    
    frame_count = 0
    total_audio_data = 0
    frame_sizes = []
    
    with open(file_path, 'rb') as f:
        while True:
            # 读取4字节头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            frame_count += 1
            
            if frame_count <= 5:  # 显示前5帧的详细信息
                print(f"帧 {frame_count}:")
                print(f"  - 类型字节: 0x{packet_type:02X} ({packet_type})")
                print(f"  - 保留字节: 0x{reserved:02X} ({reserved})")
                print(f"  - 数据长度: {data_len} 字节")
            
            # 读取音频数据
            audio_data = f.read(data_len)
            if len(audio_data) != data_len:
                print(f"警告: 帧 {frame_count} 数据长度不匹配 (期望 {data_len}, 实际 {len(audio_data)})")
                break
            
            total_audio_data += data_len
            frame_sizes.append(data_len)
            
            if frame_count > 5 and frame_count % 100 == 0:
                print(f"已处理 {frame_count} 帧...")
    
    print("\n分析结果:")
    print("=" * 50)
    print(f"总帧数: {frame_count}")
    print(f"总音频数据: {total_audio_data} 字节")
    print(f"头部数据: {frame_count * 4} 字节")
    print(f"文件利用率: {(total_audio_data / file_size * 100):.1f}%")
    
    if frame_sizes:
        avg_frame_size = sum(frame_sizes) / len(frame_sizes)
        min_frame_size = min(frame_sizes)
        max_frame_size = max(frame_sizes)
        print(f"平均帧大小: {avg_frame_size:.1f} 字节")
        print(f"最小帧大小: {min_frame_size} 字节")
        print(f"最大帧大小: {max_frame_size} 字节")
    
    # 计算预期时长 (每帧60ms)
    expected_duration = frame_count * 0.06
    print(f"预期播放时长: {expected_duration:.2f} 秒")
    
    # 格式规范检查
    print("\n格式规范检查:")
    print("=" * 50)
    
    # 检查是否符合P3格式规范
    issues = []
    
    if frame_count == 0:
        issues.append("文件中没有找到有效的音频帧")
    
    # 检查文件大小是否合理
    expected_min_size = frame_count * 4  # 至少应该有头部数据
    if file_size < expected_min_size:
        issues.append(f"文件大小异常 (小于最小期望大小 {expected_min_size})")
    
    # 检查是否有剩余字节
    expected_total_size = frame_count * 4 + total_audio_data
    if file_size != expected_total_size:
        remaining_bytes = file_size - expected_total_size
        issues.append(f"文件末尾有 {remaining_bytes} 字节未解析的数据")
    
    if issues:
        print("发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        return False
    else:
        print("✓ 文件格式符合P3规范")
        print("✓ 头部格式正确 (4字节: 类型+保留+长度)")
        print("✓ 数据包结构完整")
        print("✓ 无多余数据")
        return True

def main():
    if len(sys.argv) != 2:
        print("使用方法: python analyze_p3_format.py <p3文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    success = analyze_p3_file(file_path)
    
    if success:
        print("\n结论: 文件格式正确 ✓")
    else:
        print("\n结论: 文件格式存在问题 ✗")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
import struct
import sys

def analyze_p3_file(filename):
    print(f"分析P3文件: {filename}")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"文件总大小: {len(data)} 字节")
    print(f"前20字节 (hex): {' '.join(f'{b:02X}' for b in data[:20])}")
    
    # 分析P3数据包结构
    offset = 0
    packet_count = 0
    total_payload = 0
    payload_sizes = []
    
    while offset + 4 <= len(data):
        # 解析头部: type(1) + reserved(1) + payload_size(2, big-endian)
        type_byte = data[offset]
        reserved = data[offset + 1]
        payload_size = struct.unpack('>H', data[offset+2:offset+4])[0]
        
        if packet_count < 5:  # 显示前5个数据包的详细信息
            print(f"数据包 {packet_count+1}: type={type_byte}, reserved={reserved}, payload_size={payload_size}")
        
        payload_sizes.append(payload_size)
        total_payload += payload_size
        packet_count += 1
        
        offset += 4 + payload_size
        
        if offset > len(data):
            print(f"警告: 数据包 {packet_count} 超出文件范围")
            break
    
    print(f"\n总结:")
    print(f"数据包总数: {packet_count}")
    print(f"平均payload大小: {total_payload / packet_count:.1f} 字节" if packet_count > 0 else "无数据包")
    print(f"payload大小范围: {min(payload_sizes)} - {max(payload_sizes)} 字节" if payload_sizes else "无payload")
    
    # 判断是否为Opus格式
    if payload_sizes:
        avg_size = total_payload / packet_count
        if 50 <= avg_size <= 300:
            print("✅ 判断: 这很可能是正确的Opus编码P3文件")
        elif avg_size > 1000:
            print("❌ 判断: 这很可能是PCM数据的P3文件 (错误格式)")
        else:
            print("⚠️  判断: 格式不确定，需要进一步验证")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python analyze_p3_quick.py <p3文件路径>")
        sys.exit(1)
    
    analyze_p3_file(sys.argv[1])

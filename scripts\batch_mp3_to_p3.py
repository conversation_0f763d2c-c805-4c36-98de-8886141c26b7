#!/usr/bin/env python3
"""
MP3批量转换为P3格式的自动化脚本
扫描指定目录中的MP3文件，按照3位数字命名规则转换为P3格式
"""

import os
import sys
import glob
import shutil
from pathlib import Path
import subprocess

# 添加p3_tools目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'p3_tools'))

try:
    from convert_audio_to_p3 import encode_audio_to_opus
except ImportError:
    print("❌ 无法导入P3转换模块，请确保已安装依赖：")
    print("   cd scripts/p3_tools && pip install -r requirements.txt")
    sys.exit(1)

# 路径配置
PROJECT_ROOT = Path(__file__).parent.parent
MP3_SOURCE_DIR = PROJECT_ROOT / "MP3"
AUDIO_FILES_DIR = PROJECT_ROOT / "audio_files"
P3_TOOLS_DIR = PROJECT_ROOT / "scripts" / "p3_tools"

def create_directories():
    """创建必要的目录"""
    MP3_SOURCE_DIR.mkdir(exist_ok=True)
    AUDIO_FILES_DIR.mkdir(exist_ok=True)
    print(f"✓ 确保目录存在: {MP3_SOURCE_DIR}")
    print(f"✓ 确保目录存在: {AUDIO_FILES_DIR}")

def scan_mp3_files():
    """扫描MP3文件"""
    if not MP3_SOURCE_DIR.exists():
        print(f"❌ MP3源目录不存在: {MP3_SOURCE_DIR}")
        return []
    
    mp3_files = list(MP3_SOURCE_DIR.glob("*.mp3"))
    mp3_files.extend(MP3_SOURCE_DIR.glob("*.MP3"))
    
    if not mp3_files:
        print(f"❌ 在 {MP3_SOURCE_DIR} 中未找到MP3文件")
        return []
    
    print(f"✓ 找到 {len(mp3_files)} 个MP3文件:")
    for i, file in enumerate(mp3_files, 1):
        print(f"  {i:2d}. {file.name}")
    
    return mp3_files

def convert_mp3_to_p3(mp3_files):
    """将MP3文件转换为P3格式"""
    print("\n🔄 开始转换MP3文件...")
    
    success_count = 0
    
    for i, mp3_file in enumerate(mp3_files, 1):
        # 生成3位数字的文件名
        p3_filename = f"{i:03d}.p3"
        p3_filepath = AUDIO_FILES_DIR / p3_filename
        
        print(f"\n[{i}/{len(mp3_files)}] 转换: {mp3_file.name} -> {p3_filename}")
        
        try:
            # 使用P3转换工具
            encode_audio_to_opus(
                input_file=str(mp3_file),
                output_file=str(p3_filepath),
                target_lufs=-16.0  # 标准响度
            )
            
            # 检查输出文件是否存在
            if p3_filepath.exists() and p3_filepath.stat().st_size > 0:
                file_size = p3_filepath.stat().st_size
                print(f"✅ 转换成功: {p3_filename} ({file_size} bytes)")
                success_count += 1
            else:
                print(f"❌ 转换失败: {p3_filename} (文件不存在或为空)")
                
        except Exception as e:
            print(f"❌ 转换失败: {mp3_file.name} - {str(e)}")
    
    print(f"\n📊 转换完成: {success_count}/{len(mp3_files)} 个文件成功")
    return success_count

def test_p3_playback(p3_file):
    """测试P3文件播放"""
    print(f"\n🎵 测试播放: {p3_file.name}")
    
    try:
        # 使用P3播放工具测试
        play_script = P3_TOOLS_DIR / "play_p3.py"
        if play_script.exists():
            result = subprocess.run([
                sys.executable, str(play_script), str(p3_file)
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ P3文件播放测试成功")
                return True
            else:
                print(f"❌ P3文件播放测试失败: {result.stderr}")
                return False
        else:
            print("⚠️  播放测试脚本不存在，跳过测试")
            return True
            
    except subprocess.TimeoutExpired:
        print("✅ P3文件播放测试成功（超时停止）")
        return True
    except Exception as e:
        print(f"❌ P3文件播放测试失败: {str(e)}")
        return False

def list_converted_files():
    """列出转换后的文件"""
    p3_files = list(AUDIO_FILES_DIR.glob("*.p3"))
    
    if p3_files:
        print(f"\n📁 转换后的P3文件 ({len(p3_files)} 个):")
        for p3_file in sorted(p3_files):
            file_size = p3_file.stat().st_size
            print(f"  {p3_file.name} - {file_size:,} bytes")
    else:
        print("\n❌ 没有找到转换后的P3文件")

def main():
    """主函数"""
    print("🎵 MP3批量转换为P3格式工具")
    print("=" * 50)
    
    # 创建目录
    create_directories()
    
    # 扫描MP3文件
    mp3_files = scan_mp3_files()
    if not mp3_files:
        print(f"\n💡 使用说明:")
        print(f"1. 将MP3文件放入: {MP3_SOURCE_DIR}")
        print(f"2. 重新运行此脚本")
        return 1
    
    # 转换文件
    success_count = convert_mp3_to_p3(mp3_files)
    if success_count == 0:
        print("\n❌ 没有文件转换成功")
        return 1
    
    # 列出转换后的文件
    list_converted_files()
    
    # 测试第一个P3文件
    p3_files = list(AUDIO_FILES_DIR.glob("*.p3"))
    if p3_files:
        first_p3 = sorted(p3_files)[0]
        test_p3_playback(first_p3)
    
    print(f"\n🎉 转换完成!")
    print(f"📁 P3文件位置: {AUDIO_FILES_DIR}")
    print(f"📝 下一步: 运行 xiaozhi_real_auto.bat 编译烧录固件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

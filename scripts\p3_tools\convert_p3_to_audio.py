import struct
import sys
import numpy as np
from tqdm import tqdm
import soundfile as sf
import subprocess
import tempfile
import os

def decode_with_opusdec(opus_packets, output_file, sample_rate):
    """
    使用opusdec解码Opus数据包
    """
    opusdec_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusdec.exe')

    # 创建临时Ogg Opus文件
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_ogg:
        temp_ogg_path = temp_ogg.name

    try:
        # 重建Ogg Opus文件格式
        # 这是一个简化的实现，实际需要正确的Ogg页面结构
        create_ogg_opus_file(opus_packets, temp_ogg_path)

        # 使用opusdec解码
        cmd = [
            opusdec_path,
            '--rate', str(sample_rate),
            temp_ogg_path,
            output_file
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Opusdec解码失败: {result.stderr}")
            # 最后的备用方案：生成静音文件
            create_silence_file(output_file, sample_rate, len(opus_packets) * 0.06)
        else:
            print(f"成功使用opusdec转换为: {output_file}")

    finally:
        if os.path.exists(temp_ogg_path):
            os.unlink(temp_ogg_path)

def create_ogg_opus_file(opus_packets, output_path):
    """
    从Opus数据包创建简化的Ogg Opus文件
    这是一个非常简化的实现
    """
    with open(output_path, 'wb') as f:
        # 写入简化的Ogg头部
        # 实际实现需要正确的Ogg页面结构和OpusHead/OpusTags
        ogg_header = b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00'
        f.write(ogg_header)

        # 写入OpusHead
        opus_head = b'OpusHead\x01\x01\x00\x00\x80>\x00\x00\x00\x00\x00'
        f.write(opus_head)

        # 写入Opus数据包
        for packet in opus_packets:
            f.write(packet)

def create_silence_file(output_file, sample_rate, duration):
    """
    创建静音音频文件作为最后的备用方案
    """
    import soundfile as sf
    silence_samples = int(sample_rate * duration)
    silence_data = np.zeros(silence_samples, dtype=np.int16)
    sf.write(output_file, silence_data, sample_rate, subtype="PCM_16")
    print(f"创建了 {duration:.2f} 秒的静音文件: {output_file}")


def decode_p3_to_audio(input_file, output_file):
    sample_rate = 16000
    channels = 1

    # Extract opus packets from p3 file
    opus_packets = []

    with open(input_file, "rb") as f:
        f.seek(0, 2)
        total_size = f.tell()
        f.seek(0)

        with tqdm(total=total_size, unit="B", unit_scale=True) as pbar:
            while True:
                header = f.read(4)
                if not header or len(header) < 4:
                    break

                pkt_type, reserved, opus_len = struct.unpack(">BBH", header)
                opus_data = f.read(opus_len)
                if len(opus_data) != opus_len:
                    break

                opus_packets.append(opus_data)
                pbar.update(4 + opus_len)

    if not opus_packets:
        raise ValueError("No valid audio data found")

    # 处理真正的Opus编码数据
    print("正在解码Opus数据包...")

    # 创建临时文件来重建Opus流
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name

        # 将Opus数据包写入临时文件
        # 注意：这里需要重建Opus流格式
        for packet in opus_packets:
            temp_opus.write(packet)

    try:
        # 使用FFmpeg解码Opus数据
        ffmpeg_path = os.path.join(os.path.dirname(__file__), 'ffmpeg', 'ffmpeg-7.1.1-essentials_build', 'bin', 'ffmpeg.exe')

        # 尝试直接解码原始Opus数据
        cmd = [
            ffmpeg_path, '-y',
            '-f', 'data', '-i', temp_opus_path,  # 输入原始数据
            '-c:a', 'libopus',
            '-ar', str(sample_rate),
            '-ac', str(channels),
            output_file
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"FFmpeg解码失败: {result.stderr}")
            # 备用方案：使用opusdec
            decode_with_opusdec(opus_packets, output_file, sample_rate)
        else:
            print(f"成功转换为: {output_file}")

    finally:
        # 清理临时文件
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_p3_to_audio.py <input.p3> <output.wav>")
        sys.exit(1)

    decode_p3_to_audio(sys.argv[1], sys.argv[2])

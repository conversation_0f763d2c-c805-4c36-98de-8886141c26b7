---
title: <PERSON><PERSON><PERSON><PERSON><PERSON> 项目架构
description: 基于 Python 实现的小智语音客户端，采用模块化设计，支持多种通信协议和设备集成
sidebar: false,
pageClass: architecture-page-class
---
<script setup>
import CoreArchitecture from './components/CoreArchitecture.vue'
import StateManagement from './components/StateManagement.vue'
import DataFlow from './components/DataFlow.vue'
import ModuleDetails from './components/ModuleDetails.vue'
import TechnologyStack from './components/TechnologyStack.vue'
import ArchitectureFeatures from './components/ArchitectureFeatures.vue'
</script>

<div class="architecture-page">

# Py-Xiaozhi 项目架构

<p>基于 Python 实现的小智语音客户端，采用模块化设计，支持多种通信协议和设备集成</p>

## 核心架构
<CoreArchitecture/>

## 状态管理
<StateManagement/>

## 数据流
<DataFlow/>

## 模块详情
<ModuleDetails/>

## 技术栈
<TechnologyStack/>

## 架构特点
<ArchitectureFeatures/>
</div>

<style>
.architecture-page {
  max-width: 100%;
  padding: 0 2rem;
}
</style>
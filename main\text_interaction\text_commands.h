#pragma once

#include <string>

namespace text_interaction {

// 前向声明
class TextInteractionManager;

/**
 * @brief 文本交互命令处理器
 * 
 * 提供各种文本交互命令的实现，包括：
 * - 基础ask命令
 * - 快捷命令（hello, weather, time, joke等）
 * - 命令注册和管理
 */
class TextCommands {
public:
    /**
     * @brief 初始化文本交互命令系统
     * @param manager 文本交互管理器实例
     * @return true 初始化成功，false 初始化失败
     */
    static bool Initialize(TextInteractionManager* manager);

    /**
     * @brief 注册所有文本交互命令到ESP控制台
     */
    static void RegisterCommands();

    /**
     * @brief 获取文本交互管理器实例
     * @return 管理器实例指针，如果未初始化则返回nullptr
     */
    static TextInteractionManager* GetManager();

    // ========== 命令处理函数 ==========

    /**
     * @brief ask命令处理函数
     * 用法: ask <message>
     * 示例: ask 今天天气怎么样
     */
    static int CmdAsk(int argc, char** argv);

    /**
     * @brief hello命令处理函数
     * 用法: hello
     * 快速向小智问好
     */
    static int CmdHello(int argc, char** argv);

    /**
     * @brief weather命令处理函数
     * 用法: weather [city]
     * 示例: weather 北京
     */
    static int CmdWeather(int argc, char** argv);

    /**
     * @brief time命令处理函数
     * 用法: time
     * 询问当前时间
     */
    static int CmdTime(int argc, char** argv);

    /**
     * @brief joke命令处理函数
     * 用法: joke
     * 请小智讲个笑话
     */
    static int CmdJoke(int argc, char** argv);

    /**
     * @brief 文本交互帮助命令
     * 用法: texthelp
     * 显示所有文本交互命令的帮助信息
     */
    static int CmdTextHelp(int argc, char** argv);

    /**
     * @brief 文本交互状态命令
     * 用法: textstatus
     * 显示文本交互系统的当前状态
     */
    static int CmdTextStatus(int argc, char** argv);

private:
    /**
     * @brief 验证参数并构建消息
     * @param argc 参数数量
     * @param argv 参数数组
     * @param start_index 开始拼接的参数索引
     * @param message 输出的消息字符串
     * @return true 参数有效，false 参数无效
     */
    static bool BuildMessageFromArgs(int argc, char** argv, int start_index, std::string& message);

    /**
     * @brief 打印命令使用说明
     * @param command 命令名称
     * @param usage 使用方法
     * @param example 示例（可选）
     */
    static void PrintUsage(const char* command, const char* usage, const char* example = nullptr);

    /**
     * @brief 发送消息并显示状态
     * @param message 要发送的消息
     * @param action_description 动作描述（如"查询天气"）
     * @return true 发送成功，false 发送失败
     */
    static bool SendMessageWithStatus(const std::string& message, const char* action_description);

private:
    static TextInteractionManager* manager_;  ///< 文本交互管理器实例
    static bool initialized_;                 ///< 初始化状态标志
};

} // namespace text_interaction

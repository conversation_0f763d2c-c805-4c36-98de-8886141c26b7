# 系统依赖安装

## 概述

本文档提供py-xiaozhi项目的完整依赖安装指南，包括系统级依赖和Python环境配置。请按照文档顺序进行安装。

## 系统依赖安装

### Windows

#### 多媒体组件
```bash
# 使用 Scoop 安装（推荐）
scoop install ffmpeg

# 或使用 Conda
conda install -c conda-forge ffmpeg opus
```

#### 手动安装FFmpeg
1. 下载：[BtbN/FFmpeg-Builds](https://github.com/BtbN/FFmpeg-Builds/releases)
2. 解压后将`bin`目录添加至系统环境变量`PATH`

#### Opus音频编解码器
- 项目已包含`opus.dll`，通常无需额外安装
- 如遇问题，可将`/libs/windows/opus.dll`复制到：
  - 应用程序运行目录
  - `C:\Windows\System32`

### Linux (Debian/Ubuntu)

#### 核心依赖
```bash
# 更新包管理器
sudo apt-get update

# 安装核心依赖
sudo apt-get install portaudio19-dev ffmpeg libopus0 libopus-dev \
                     build-essential python3-venv python3-pip \
                     libasound2-dev
```

#### 音频系统依赖（选择其一）

**PulseAudio（推荐）**
```bash
sudo apt-get install pulseaudio-utils
```

**ALSA音频系统**
```bash
sudo apt-get install alsa-utils
```

**ALSA + 交互工具**
```bash
sudo apt-get install alsa-utils expect
```

#### GUI支持
```bash
# PyQt5 GUI依赖
sudo apt-get install python3-pyqt5 python3-pyqt5.qtmultimedia \
                     qtbase5-dev qt5-qmake qtmultimedia5-dev
```

#### 开发工具（可选）
```bash
# 编译依赖
sudo apt-get install gcc g++ make cmake pkg-config
```

### macOS

```bash
# 使用 Homebrew 安装
brew install portaudio opus ffmpeg gfortran
brew upgrade tcl-tk

# 安装 Xcode 命令行工具（如未安装）
xcode-select --install
```

## Python环境配置

### 方式一：使用Miniconda（推荐）

#### 1. 下载Miniconda

| 操作系统 | 架构 | 下载命令 |
|---------|------|----------|
| **Linux** | x86_64 | `wget -O Miniconda3-latest-Linux-x86_64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh` |
| **Linux** | ARM64 | `wget -O Miniconda3-latest-Linux-aarch64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-aarch64.sh` |
| **macOS** | Intel | `wget -O Miniconda3-latest-MacOSX-x86_64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh` |
| **macOS** | Apple Silicon | `wget -O Miniconda3-latest-MacOSX-arm64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-arm64.sh` |
| **Windows** | x86_64 | [下载链接](https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe) |

#### 2. 安装Miniconda

**Linux/macOS**
```bash
# 添加执行权限
chmod +x Miniconda3-latest-*.sh

# 运行安装程序
./Miniconda3-latest-*.sh
```

安装过程中的选择：
1. 许可协议：按`Enter`或`q`跳过，输入`yes`接受
2. 安装路径：使用默认路径（推荐）
3. 初始化：输入`yes`（推荐）

#### 3. 配置环境（如需要）

```bash
# 如环境变量未自动配置
echo 'export PATH="$HOME/miniconda3/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 初始化conda
conda init
```

#### 4. 验证安装

```bash
conda --version
```

#### 5. 优化配置

```bash
# 关闭自动激活base环境
conda config --set auto_activate_base false

# 添加conda-forge频道
conda config --add channels conda-forge
```

### 方式二：使用系统Python + venv

```bash
# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
# Linux/macOS
source .venv/bin/activate
# Windows
.venv\Scripts\activate
```

## 包管理器优化

### 换源工具（推荐）

```bash
# Windows（PowerShell管理员权限）
winget install RubyMetric.chsrc --source winget

# Linux/macOS
wget -O- aslant.top/chsrc.sh | sudo bash

# 配置pip源
chsrc set pip
```

### 手动配置pip源

```bash
# 使用阿里云镜像源
pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
pip config set install.trusted-host mirrors.aliyun.com
```

## 项目依赖安装

### 1. 创建项目环境

```bash
# 使用Conda（推荐）
conda create -n py-xiaozhi python=3.10 -y
conda activate py-xiaozhi

# 或使用venv
python3 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate   # Windows
```

### 2. 安装Python依赖

```bash
# Linux/Windows
pip install -r requirements.txt

# macOS
pip install -r requirements_mac.txt
```

### 3. 验证安装

```bash
# 检查关键依赖
python -c "import sounddevice; print('SoundDevice OK')"
python -c "import opuslib; print('Opus OK')"
python -c "import PyQt5.QtCore; print('PyQt5 OK')"
```

## 故障排除

### 常见问题

#### SoundDevice安装失败
```bash
# Ubuntu/Debian
sudo apt-get install portaudio19-dev libasound2-dev

# macOS
brew install portaudio

# Windows
# SoundDevice通常可以直接通过pip安装
pip install sounddevice
```

#### PyQt5安装失败
```bash
# Ubuntu/Debian
sudo apt-get install python3-pyqt5 qtbase5-dev

# macOS
brew install pyqt5

# Windows
# PyQt5通常可以直接通过pip安装
pip install PyQt5
```

#### Opus库缺失
- macos用户请使用conda安装，不然得自行解决软连接
```bash
# 使用conda安装
conda install -c conda-forge opus
# 软连接
nano ~/.zshrc 
export DYLD_LIBRARY_PATH=/opt/homebrew/lib:$DYLD_LIBRARY_PATH
source ~/.zshrc 

# 或检查系统库
# Linux
sudo apt-get install libopus0 libopus-dev
# macOS
brew install opus
```

#### 权限问题（Linux）
```bash
# 将用户添加到audio组
sudo usermod -a -G audio $USER

# 重新登录后生效
```

## 版本要求

- **Python**: 3.9.13+ (推荐3.10，最高3.11)
- **PyQt5**: 5.15+
- **SoundDevice**: 0.4.4+
- **FFmpeg**: 4.0+
- **Opus**: 1.3+
- **PortAudio**: 19.0+

## 注意事项

1. 建议使用Conda环境管理依赖
2. 不要与esp32-server共用Conda环境
3. macOS用户必须使用`requirements_mac.txt`
4. Windows用户无需手动安装opus.dll
5. 项目使用SoundDevice替代PyAudio，使用PyQt5作为GUI框架
6. 确保系统依赖安装完成后再安装Python依赖
7. 使用国内镜像源可提高下载速度
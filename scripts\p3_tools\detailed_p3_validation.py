#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的P3格式验证工具
根据P3格式说明进行完整的规范检查
"""

import struct
import os
import sys
import numpy as np

def validate_p3_format(file_path):
    """
    详细验证P3文件是否符合所有格式要求
    
    P3格式规范:
    - 每个音频帧由一个4字节的头部和一个Opus编码的数据包组成
    - 头部格式：[1字节类型, 1字节保留, 2字节长度]
    - 采样率固定为16000Hz，单声道
    - 每帧时长为60ms
    """
    
    print(f"详细验证P3文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 错误: 文件 {file_path} 不存在")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"📁 文件大小: {file_size:,} 字节")
    
    # 验证结果
    validation_results = {
        "header_format": False,
        "frame_structure": False,
        "frame_duration": False,
        "data_consistency": False,
        "sample_rate_compliance": False
    }
    
    issues = []
    frame_count = 0
    total_audio_data = 0
    frame_sizes = []
    
    # 预期的帧参数 (16000Hz, 60ms, 单声道, 16位)
    EXPECTED_SAMPLE_RATE = 16000
    EXPECTED_FRAME_DURATION_MS = 60
    EXPECTED_CHANNELS = 1
    EXPECTED_BITS_PER_SAMPLE = 16
    
    # 计算预期的每帧样本数和字节数
    expected_samples_per_frame = int(EXPECTED_SAMPLE_RATE * EXPECTED_FRAME_DURATION_MS / 1000)
    expected_bytes_per_frame = expected_samples_per_frame * EXPECTED_CHANNELS * (EXPECTED_BITS_PER_SAMPLE // 8)
    
    print(f"📊 预期参数:")
    print(f"   - 采样率: {EXPECTED_SAMPLE_RATE} Hz")
    print(f"   - 帧时长: {EXPECTED_FRAME_DURATION_MS} ms")
    print(f"   - 声道数: {EXPECTED_CHANNELS}")
    print(f"   - 位深度: {EXPECTED_BITS_PER_SAMPLE} bit")
    print(f"   - 每帧样本数: {expected_samples_per_frame}")
    print(f"   - 每帧字节数: {expected_bytes_per_frame}")
    print()
    
    with open(file_path, 'rb') as f:
        while True:
            # 读取4字节头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            frame_count += 1
            
            # 验证头部格式
            if frame_count == 1:
                print(f"🔍 头部格式检查:")
                print(f"   - 类型字节: 0x{packet_type:02X} ({packet_type})")
                print(f"   - 保留字节: 0x{reserved:02X} ({reserved})")
                print(f"   - 数据长度: {data_len} 字节")
                
                # 检查头部格式是否正确
                if packet_type == 0 and reserved == 0:
                    validation_results["header_format"] = True
                    print("   ✅ 头部格式正确")
                else:
                    issues.append(f"头部格式异常: 类型={packet_type}, 保留={reserved}")
                    print("   ❌ 头部格式异常")
            
            # 读取音频数据
            audio_data = f.read(data_len)
            if len(audio_data) != data_len:
                issues.append(f"帧 {frame_count} 数据长度不匹配 (期望 {data_len}, 实际 {len(audio_data)})")
                break
            
            total_audio_data += data_len
            frame_sizes.append(data_len)
    
    print(f"\n📈 统计信息:")
    print(f"   - 总帧数: {frame_count}")
    print(f"   - 总音频数据: {total_audio_data:,} 字节")
    print(f"   - 头部数据: {frame_count * 4:,} 字节")
    
    if frame_sizes:
        avg_frame_size = sum(frame_sizes) / len(frame_sizes)
        min_frame_size = min(frame_sizes)
        max_frame_size = max(frame_sizes)
        print(f"   - 平均帧大小: {avg_frame_size:.1f} 字节")
        print(f"   - 帧大小范围: {min_frame_size} - {max_frame_size} 字节")
        
        # 检查帧大小一致性
        if min_frame_size == max_frame_size == expected_bytes_per_frame:
            validation_results["frame_structure"] = True
            print("   ✅ 帧大小一致且符合预期")
        elif min_frame_size == max_frame_size:
            print(f"   ⚠️  帧大小一致但不符合预期 (实际: {min_frame_size}, 预期: {expected_bytes_per_frame})")
        else:
            issues.append(f"帧大小不一致: {min_frame_size} - {max_frame_size}")
            print("   ❌ 帧大小不一致")
    
    # 计算实际时长
    actual_duration = frame_count * EXPECTED_FRAME_DURATION_MS / 1000
    print(f"   - 计算播放时长: {actual_duration:.2f} 秒")
    
    # 验证帧时长
    if frame_count > 0:
        validation_results["frame_duration"] = True
        print("   ✅ 帧时长计算正确 (60ms/帧)")
    
    # 验证数据一致性
    expected_total_size = frame_count * 4 + total_audio_data
    if file_size == expected_total_size:
        validation_results["data_consistency"] = True
        print("   ✅ 数据完整性检查通过")
    else:
        remaining_bytes = file_size - expected_total_size
        issues.append(f"文件末尾有 {remaining_bytes} 字节未解析的数据")
        print(f"   ❌ 数据完整性检查失败 (多余 {remaining_bytes} 字节)")
    
    # 验证采样率合规性 (通过帧大小推断)
    if frame_sizes and all(size == expected_bytes_per_frame for size in frame_sizes):
        validation_results["sample_rate_compliance"] = True
        print("   ✅ 采样率合规性检查通过")
    else:
        print("   ❌ 采样率合规性检查失败")
    
    print(f"\n🔍 详细验证结果:")
    print("=" * 60)
    
    all_passed = True
    for check, passed in validation_results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        check_name = {
            "header_format": "头部格式检查",
            "frame_structure": "帧结构检查", 
            "frame_duration": "帧时长检查",
            "data_consistency": "数据一致性检查",
            "sample_rate_compliance": "采样率合规性检查"
        }[check]
        print(f"   {check_name}: {status}")
        if not passed:
            all_passed = False
    
    if issues:
        print(f"\n⚠️  发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    print(f"\n🎯 最终结论:")
    if all_passed and not issues:
        print("✅ 文件完全符合P3格式规范")
        return True
    else:
        print("❌ 文件不完全符合P3格式规范")
        return False

def main():
    if len(sys.argv) != 2:
        print("使用方法: python detailed_p3_validation.py <p3文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    success = validate_p3_format(file_path)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量转换AI智能语音盒目录中的音频文件为P3格式
"""

import os
import sys
import glob
from pathlib import Path
from convert_audio_to_p3 import encode_audio_to_opus

def batch_convert_ai_voice():
    """
    批量转换AI智能语音盒目录中的音频文件
    """
    # 源目录和目标目录
    source_dir = "AI智能语音盒"
    output_dir = "output"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有mp3文件
    mp3_pattern = os.path.join(source_dir, "*.mp3")
    mp3_files = glob.glob(mp3_pattern)
    
    if not mp3_files:
        print(f"在 {source_dir} 目录中没有找到mp3文件")
        return
    
    print(f"找到 {len(mp3_files)} 个mp3文件需要转换")
    print("=" * 50)
    
    success_count = 0
    failed_count = 0
    
    for mp3_file in sorted(mp3_files):
        # 获取文件名（不含扩展名）
        file_name = Path(mp3_file).stem
        
        # 构建输出文件路径
        output_file = os.path.join(output_dir, f"{file_name}.p3")
        
        print(f"正在转换: {mp3_file} -> {output_file}")
        
        try:
            # 调用转换函数，禁用响度标准化
            encode_audio_to_opus(mp3_file, output_file, target_lufs=None)
            print(f"✅ 转换成功: {file_name}.p3")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 转换失败: {file_name}.mp3 - {str(e)}")
            failed_count += 1
        
        print("-" * 30)
    
    print("\n" + "=" * 50)
    print(f"批量转换完成!")
    print(f"成功: {success_count} 个文件")
    print(f"失败: {failed_count} 个文件")
    print(f"输出目录: {os.path.abspath(output_dir)}")

if __name__ == "__main__":
    batch_convert_ai_voice()

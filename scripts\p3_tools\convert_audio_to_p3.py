# convert audio files to protocol v3 stream
import librosa
import struct
import sys
import tqdm
import numpy as np
import argparse
import pyloudnorm as pyln
import subprocess
import tempfile
import os

def encode_audio_to_opus(input_file, output_file, target_lufs=None):
    # Load audio file using librosa
    audio, sample_rate = librosa.load(input_file, sr=None, mono=False, dtype=np.float32)

    # Convert to mono if stereo
    if audio.ndim == 2:
        audio = librosa.to_mono(audio)

    if target_lufs is not None:
        print("Note: Automatic loudness adjustment is enabled, which may cause", file=sys.stderr)
        print("      audio distortion. If the input audio has already been ", file=sys.stderr)
        print("      loudness-adjusted or if the input audio is TTS audio, ", file=sys.stderr)
        print("      please use the `-d` parameter to disable loudness adjustment.", file=sys.stderr)
        meter = pyln.Meter(sample_rate)
        current_loudness = meter.integrated_loudness(audio)
        audio = pyln.normalize.loudness(audio, current_loudness, target_lufs)
        print(f"Adjusted loudness: {current_loudness:.1f} LUFS -> {target_lufs} LUFS")

    # Convert sample rate to 16000Hz if necessary
    target_sample_rate = 16000
    if sample_rate != target_sample_rate:
        audio = librosa.resample(audio, orig_sr=sample_rate, target_sr=target_sample_rate)
        sample_rate = target_sample_rate

    # Convert audio data back to int16 after processing
    audio = (audio * 32767).astype(np.int16)

    # Create temporary WAV file for ffmpeg
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
        temp_wav_path = temp_wav.name
        # Write WAV header and data
        import wave
        with wave.open(temp_wav_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(target_sample_rate)
            wav_file.writeframes(audio.tobytes())

    # Create temporary opus file
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name

    try:
        # Use ffmpeg to encode to opus
        cmd = [
            'ffmpeg', '-y', '-i', temp_wav_path,
            '-c:a', 'libopus',
            '-b:a', '64k',
            '-frame_duration', '60',
            temp_opus_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"FFmpeg failed: {result.stderr}")

        # Read opus file and convert to p3 format
        with open(temp_opus_path, 'rb') as opus_file:
            opus_data = opus_file.read()

        # For now, just copy the opus data as-is
        # TODO: Parse opus packets and reformat for p3 protocol
        with open(output_file, 'wb') as f:
            f.write(opus_data)

    finally:
        # Clean up temporary files
        if os.path.exists(temp_wav_path):
            os.unlink(temp_wav_path)
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert audio to Opus with loudness normalization')
    parser.add_argument('input_file', help='Input audio file')
    parser.add_argument('output_file', help='Output .opus file')
    parser.add_argument('-l', '--lufs', type=float, default=-16.0,
                       help='Target loudness in LUFS (default: -16)')
    parser.add_argument('-d', '--disable-loudnorm', action='store_true',
                       help='Disable loudness normalization')
    args = parser.parse_args()

    target_lufs = None if args.disable_loudnorm else args.lufs
    encode_audio_to_opus(args.input_file, args.output_file, target_lufs)
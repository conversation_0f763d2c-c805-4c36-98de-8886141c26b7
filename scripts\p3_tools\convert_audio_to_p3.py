# convert audio files to protocol v3 stream
import librosa
import struct
import sys
import tqdm
import numpy as np
import argparse
import pyloudnorm as pyln
import subprocess
import tempfile
import os

def encode_audio_to_opus(input_file, output_file, target_lufs=None):
    # Load audio file using librosa
    audio, sample_rate = librosa.load(input_file, sr=None, mono=False, dtype=np.float32)

    # Convert to mono if stereo
    if audio.ndim == 2:
        audio = librosa.to_mono(audio)

    if target_lufs is not None:
        print("Note: Automatic loudness adjustment is enabled, which may cause", file=sys.stderr)
        print("      audio distortion. If the input audio has already been ", file=sys.stderr)
        print("      loudness-adjusted or if the input audio is TTS audio, ", file=sys.stderr)
        print("      please use the `-d` parameter to disable loudness adjustment.", file=sys.stderr)
        meter = pyln.Meter(sample_rate)
        current_loudness = meter.integrated_loudness(audio)
        audio = pyln.normalize.loudness(audio, current_loudness, target_lufs)
        print(f"Adjusted loudness: {current_loudness:.1f} LUFS -> {target_lufs} LUFS")

    # Convert sample rate to 16000Hz if necessary
    target_sample_rate = 16000
    if sample_rate != target_sample_rate:
        audio = librosa.resample(audio, orig_sr=sample_rate, target_sr=target_sample_rate)
        sample_rate = target_sample_rate

    # Convert audio data back to int16 after processing
    audio = (audio * 32767).astype(np.int16)

    # Create temporary WAV file for ffmpeg
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
        temp_wav_path = temp_wav.name
        # Write WAV header and data
        import wave
        with wave.open(temp_wav_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(target_sample_rate)
            wav_file.writeframes(audio.tobytes())

    # Create temporary opus file
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name

    try:
        # Use ffmpeg to encode to opus
        ffmpeg_path = os.path.join(os.path.dirname(__file__), 'ffmpeg', 'ffmpeg-7.1.1-essentials_build', 'bin', 'ffmpeg.exe')
        cmd = [
            ffmpeg_path, '-y', '-i', temp_wav_path,
            '-c:a', 'libopus',
            '-b:a', '64k',
            '-frame_duration', '60',
            temp_opus_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"FFmpeg failed: {result.stderr}")

        # 使用opusenc工具生成真正的Opus编码数据
        opusenc_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusenc.exe')

        # 创建临时opus文件用于存储原始opus数据包
        with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_raw_opus:
            temp_raw_opus_path = temp_raw_opus.name

        try:
            # 使用opusenc编码为原始opus格式
            cmd = [
                opusenc_path,
                '--bitrate', '64',
                '--framesize', '60',
                '--raw',
                '--raw-rate', str(target_sample_rate),
                '--raw-chan', '1',
                '--raw-bits', '16',
                temp_wav_path,
                temp_raw_opus_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise RuntimeError(f"Opusenc failed: {result.stderr}")

            # 读取opus编码数据并分割成数据包
            with open(temp_raw_opus_path, 'rb') as opus_file:
                opus_data = opus_file.read()

            # 将opus数据分割成60ms帧的数据包
            # 每个opus数据包大约64-128字节（64kbps，60ms帧）
            frame_duration_ms = 60
            expected_packet_size = int(64000 * frame_duration_ms / 8 / 1000)  # 约480字节

            # 由于opus编码器输出的是连续的数据流，我们需要按固定大小分割
            # 这是一个简化的方法，实际应该解析opus数据包头
            packet_size = min(expected_packet_size, 200)  # 限制最大包大小

            with open(output_file, 'wb') as f:
                offset = 0
                packet_count = 0

                while offset < len(opus_data):
                    # 获取当前数据包
                    remaining = len(opus_data) - offset
                    current_packet_size = min(packet_size, remaining)

                    if current_packet_size == 0:
                        break

                    opus_packet = opus_data[offset:offset + current_packet_size]
                    offset += current_packet_size

                    # 写入P3格式的头部
                    packet_type = 0  # 音频数据包类型
                    reserved = 0     # 保留字节
                    data_len = len(opus_packet)

                    # 写入4字节头部: [类型, 保留, 长度(2字节大端序)]
                    header = struct.pack('>BBH', packet_type, reserved, data_len)
                    f.write(header)

                    # 写入opus编码数据
                    f.write(opus_packet)
                    packet_count += 1

                print(f"Generated {packet_count} opus packets")

        finally:
            # 清理临时文件
            if os.path.exists(temp_raw_opus_path):
                os.unlink(temp_raw_opus_path)

    finally:
        # Clean up temporary files
        if os.path.exists(temp_wav_path):
            os.unlink(temp_wav_path)
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert audio to Opus with loudness normalization')
    parser.add_argument('input_file', help='Input audio file')
    parser.add_argument('output_file', help='Output .opus file')
    parser.add_argument('-l', '--lufs', type=float, default=-16.0,
                       help='Target loudness in LUFS (default: -16)')
    parser.add_argument('-d', '--disable-loudnorm', action='store_true',
                       help='Disable loudness normalization')
    args = parser.parse_args()

    target_lufs = None if args.disable_loudnorm else args.lufs
    encode_audio_to_opus(args.input_file, args.output_file, target_lufs)
param(
    [string]$Phase = "BUILD",
    [int]$Duration = 300
)

Write-Host "Auto Monitor Starting - Phase: $Phase" -ForegroundColor Green
Write-Host "AUTO_MONITOR_START: $(Get-Date)" -ForegroundColor Yellow
Write-Host "AUTO_MONITOR_DURATION: $Duration seconds" -ForegroundColor Yellow

# Create monitor log file
$logFile = "monitor_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$startTime = Get-Date

Write-Host "Monitor log file: $logFile" -ForegroundColor Cyan

# 监控函数
function Start-AutoMonitoring {
    param($Duration, $LogFile)
    
    $endTime = (Get-Date).AddSeconds($Duration)
    $processFound = $false
    
    Write-Host "Starting auto process monitoring..." -ForegroundColor Green
    
    while ((Get-Date) -lt $endTime) {
        # 检查是否有ESP32相关进程
        $espProcesses = Get-Process | Where-Object { 
            $_.ProcessName -like "*python*" -or 
            $_.ProcessName -like "*idf*" -or 
            $_.ProcessName -like "*ninja*" -or
            $_.ProcessName -like "*cmake*"
        }
        
        if ($espProcesses) {
            if (-not $processFound) {
                Write-Host "ESP32 build process detected" -ForegroundColor Green
                $processFound = $true
                Add-Content $LogFile "$(Get-Date): ESP32 build process started"
            }
            
            # 监控进程状态
            foreach ($proc in $espProcesses) {
                $cpuUsage = Get-Counter "\Process($($proc.ProcessName))\% Processor Time" -ErrorAction SilentlyContinue
                if ($cpuUsage) {
                    $usage = [math]::Round($cpuUsage.CounterSamples[0].CookedValue, 2)
                    Write-Host "📊 进程 $($proc.ProcessName): CPU使用率 $usage%" -ForegroundColor Blue
                    Add-Content $LogFile "$(Get-Date): 进程 $($proc.ProcessName) CPU: $usage%"
                }
            }
        } else {
            if ($processFound) {
                Write-Host "⚠️ ESP32进程已结束" -ForegroundColor Yellow
                Add-Content $LogFile "$(Get-Date): ESP32进程已结束"
                break
            }
        }
        
        # 检查串口活动
        $comPorts = Get-WmiObject -Class Win32_SerialPort | Where-Object { $_.DeviceID -eq "COM13" }
        if ($comPorts) {
            Write-Host "COM13 port active" -ForegroundColor Cyan
            Add-Content $LogFile "$(Get-Date): COM13 port activity detected"
        }

        Start-Sleep -Seconds 2
    }

    Write-Host "Auto monitoring complete" -ForegroundColor Green
    Add-Content $LogFile "$(Get-Date): Auto monitoring ended"
}

# Try to call read-terminal functionality
try {
    Write-Host "Attempting to start read-terminal monitoring..." -ForegroundColor Yellow

    # Method 1: Check if read-terminal command exists
    $readTerminalCmd = Get-Command "read-terminal" -ErrorAction SilentlyContinue
    if ($readTerminalCmd) {
        Write-Host "Found read-terminal command, starting auto monitoring" -ForegroundColor Green
        Start-Process -FilePath "read-terminal" -ArgumentList "--auto", "--duration=$Duration" -NoNewWindow
    } else {
        Write-Host "⚠️ 未找到read-terminal命令，使用内置监控" -ForegroundColor Yellow
        
        # 方案2: 尝试VS Code命令
        $codeCmd = Get-Command "code" -ErrorAction SilentlyContinue
        if ($codeCmd) {
            Write-Host "🔄 尝试通过VS Code启动监控..." -ForegroundColor Yellow
            Start-Process -FilePath "code" -ArgumentList "--command", "workbench.action.terminal.readFromTerminal" -NoNewWindow -ErrorAction SilentlyContinue
        }
        
        # 方案3: 使用内置监控
        Write-Host "🤖 启动内置自动监控系统" -ForegroundColor Green
        Start-AutoMonitoring -Duration $Duration -LogFile $logFile
    }
} catch {
    Write-Host "❌ 自动监控启动失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "🔄 回退到基础监控模式" -ForegroundColor Yellow
    Start-AutoMonitoring -Duration $Duration -LogFile $logFile
}

$endTime = Get-Date
$totalTime = ($endTime - $startTime).TotalSeconds

Write-Host "AUTO_MONITOR_END: $endTime" -ForegroundColor Yellow
Write-Host "AUTO_MONITOR_TOTAL_TIME: $totalTime seconds" -ForegroundColor Yellow
Write-Host "Monitoring log saved to: $logFile" -ForegroundColor Cyan

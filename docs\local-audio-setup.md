# 本地音频文件设置指南

## 📁 **音频文件存储位置**

本地音频文件存储在ESP32的专用音频SPIFFS分区中：
```
/audio/
├── 001.mp3
├── 002.wav
├── 003.p3
└── ...
```

**分区信息：**
- 分区名称: `audio`
- 分区大小: 3MB
- 挂载路径: `/audio`

## 🎵 **支持的音频格式**

- **MP3** - 标准MP3格式
- **WAV** - 标准WAV格式  
- **P3** - 小智专用的Opus流式格式（推荐）

## 📝 **文件命名规则**

音频文件必须按照以下格式命名：
- **格式**: `XXX.扩展名`
- **编号**: 3位数字（001, 002, 003, ...）
- **扩展名**: .mp3, .wav, .p3

### ✅ **正确示例**
```
001.mp3  ✓
002.wav  ✓
003.p3   ✓
010.mp3  ✓
099.wav  ✓
```

### ❌ **错误示例**
```
1.mp3     ❌ (不是3位数)
01.wav    ❌ (不是3位数)
hello.mp3 ❌ (不是数字)
001.txt   ❌ (不支持的格式)
```

## 🔧 **如何添加音频文件**

### 🚀 **方法1：使用自动化脚本（推荐）**

#### **步骤1：准备音频文件**
```bash
# 在项目根目录创建音频文件目录
mkdir J:\xiaozhi-esp32\audio_files

# 将您的MP3文件按格式放入目录
# 文件命名: 001.mp3, 002.mp3, 003.mp3 等
```

#### **步骤2：运行构建脚本**
```bash
# 进入项目目录
cd J:\xiaozhi-esp32

# 运行音频SPIFFS构建脚本
python scripts\build_audio_spiffs.py
```

脚本会自动：
1. 检查音频文件格式和命名
2. 构建SPIFFS镜像
3. 询问是否立即烧录到ESP32

### 📋 **方法2：手动烧录**

如果您已经有SPIFFS镜像文件：

```bash
# 烧录音频分区
esptool.py --chip esp32s3 --port COM3 --baud 921600 write_flash 0x100000 audio.bin
```

### 🔄 **方法3：重新编译固件**

修改分区表后需要重新编译：
```bash
# 清理并重新编译
J:\xiaozhi-esp32\clean_build_flash.bat
```

## 🎮 **测试命令**

### 查看可用音频文件
```bash
xiaozhi> local
```
这会显示所有可用的音频文件列表。

### 播放指定音频
```bash
xiaozhi> local 001    # 播放 001.mp3/wav/p3
xiaozhi> local 002    # 播放 002.mp3/wav/p3
xiaozhi> local 010    # 播放 010.mp3/wav/p3
```

## 📊 **音频文件建议**

### 文件大小
- **建议**: 每个文件 < 1MB
- **原因**: ESP32存储空间有限

### 音频质量
- **采样率**: 16kHz（推荐）
- **声道**: 单声道
- **比特率**: 64-128kbps

### P3格式优势
- **压缩率高**: 比MP3更小
- **播放效率**: 专为ESP32优化
- **转换工具**: 使用项目中的 `scripts/p3_tools/`

## 🔍 **故障排除**

### 音频文件不显示
1. 检查文件命名是否正确（3位数字）
2. 检查文件扩展名是否支持
3. 确认文件在 `/audio/` 目录中
4. 使用 `local` 命令查看系统识别的文件

### 播放失败
1. 检查音频文件是否损坏
2. 确认音频格式是否支持
3. 检查ESP32存储空间是否充足
4. 确认音频分区已正确挂载到 `/audio`
5. 查看串口日志获取详细错误信息

### 文件上传失败
1. 确认SPIFFS分区大小足够
2. 检查文件路径是否正确
3. 确认ESP32连接正常

## 📋 **示例音频文件列表**

建议准备以下测试音频：

```
001.p3  - "欢迎使用小智"
002.p3  - "系统启动完成"
003.p3  - "连接成功"
004.p3  - "播放测试"
005.p3  - "音频系统正常"
```

## 🚀 **快速开始**

1. **准备音频文件**
   ```bash
   # 转换MP3到P3格式
   python scripts/p3_tools/convert_audio_to_p3.py input.mp3 001.p3
   ```

2. **上传到ESP32**
   - 将文件放入 `spiffs_image/` 目录
   - 重新烧录固件

3. **测试播放**
   ```bash
   xiaozhi> local        # 查看文件列表
   xiaozhi> local 001    # 播放测试
   ```

## 📞 **技术支持**

如果遇到问题，请：
1. 查看串口日志
2. 确认文件格式和命名
3. 检查SPIFFS分区状态
4. 使用 `audiohelp` 命令查看完整帮助

#include "text_commands.h"
#include "text_interaction_manager.h"
#include "esp_console.h"
#include "esp_log.h"
#include <cstdio>
#include <cstring>

static const char* TAG = "TextCommands";

namespace text_interaction {

// 静态成员变量定义
TextInteractionManager* TextCommands::manager_ = nullptr;
bool TextCommands::initialized_ = false;

bool TextCommands::Initialize(TextInteractionManager* manager) {
    if (initialized_) {
        ESP_LOGW(TAG, "TextCommands already initialized");
        return true;
    }

    if (!manager) {
        ESP_LOGE(TAG, "TextInteractionManager is null");
        return false;
    }

    manager_ = manager;
    initialized_ = true;
    
    ESP_LOGI(TAG, "TextCommands initialized successfully");
    return true;
}

void TextCommands::RegisterCommands() {
    if (!initialized_ || !manager_) {
        ESP_LOGE(TAG, "TextCommands not properly initialized");
        return;
    }

    ESP_LOGI(TAG, "Registering text interaction commands...");

    // ask命令 - 发送任意文本
    const esp_console_cmd_t cmd_ask = {
        .command = "ask",
        .help = "Send text to Xiaozhi AI and get audio response",
        .hint = "[text_message]",
        .func = CmdAsk,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_ask));

    // say命令 - 简化版ask命令
    const esp_console_cmd_t cmd_say = {
        .command = "say",
        .help = "Simple text command (alternative to ask)",
        .hint = "[text_message]",
        .func = CmdSay,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_say));

    // hello命令 - 快速问候
    const esp_console_cmd_t cmd_hello = {
        .command = "hello",
        .help = "Quick greeting to Xiaozhi",
        .hint = nullptr,
        .func = CmdHello,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_hello));

    // weather命令 - 天气查询
    const esp_console_cmd_t cmd_weather = {
        .command = "weather",
        .help = "Ask about weather",
        .hint = "[city]",
        .func = CmdWeather,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_weather));

    // time命令 - 时间查询
    const esp_console_cmd_t cmd_time = {
        .command = "time",
        .help = "Ask about current time",
        .hint = nullptr,
        .func = CmdTime,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_time));

    // joke命令 - 讲笑话
    const esp_console_cmd_t cmd_joke = {
        .command = "joke",
        .help = "Ask Xiaozhi to tell a joke",
        .hint = nullptr,
        .func = CmdJoke,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_joke));

    // texthelp命令 - 文本交互帮助
    const esp_console_cmd_t cmd_texthelp = {
        .command = "texthelp",
        .help = "Show text interaction commands help",
        .hint = nullptr,
        .func = CmdTextHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_texthelp));

    // textstatus命令 - 文本交互状态
    const esp_console_cmd_t cmd_textstatus = {
        .command = "textstatus",
        .help = "Show text interaction system status",
        .hint = nullptr,
        .func = CmdTextStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_textstatus));

    ESP_LOGI(TAG, "All text interaction commands registered successfully");
}

TextInteractionManager* TextCommands::GetManager() {
    return manager_;
}

// ========== 命令处理函数实现 ==========

int TextCommands::CmdAsk(int argc, char** argv) {
    // 调试信息
    printf("🔍 Debug: argc=%d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("🔍 Debug: argv[%d]='%s'\n", i, argv[i]);
    }

    if (argc < 2) {
        printf("💡 提示: 请使用以下方式之一:\n");
        printf("   1. ask \"今天天气怎么样\"  (推荐，使用引号)\n");
        printf("   2. ask hello             (单个英文词)\n");
        printf("   3. ask weather           (预设命令)\n");
        return 1;
    }

    std::string message;
    if (!BuildMessageFromArgs(argc, argv, 1, message)) {
        printf("❌ 错误: 无法构建消息\n");
        return 1;
    }

    printf("🔍 Debug: final message='%s'\n", message.c_str());
    return SendMessageWithStatus(message, "发送消息") ? 0 : 1;
}

int TextCommands::CmdSay(int argc, char** argv) {
    // 简化版本的ask命令，专门处理中文输入
    printf("🔍 Say Debug: argc=%d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("🔍 Say Debug: argv[%d]='%s'\n", i, argv[i]);
    }

    if (argc < 2) {
        printf("💡 say命令用法:\n");
        printf("   say hello\n");
        printf("   say \"你好小智\"\n");
        printf("   say weather\n");
        return 1;
    }

    std::string message;
    if (!BuildMessageFromArgs(argc, argv, 1, message)) {
        printf("❌ 错误: 无法构建消息\n");
        return 1;
    }

    printf("📤 发送消息: %s\n", message.c_str());
    return SendMessageWithStatus(message, "发送消息") ? 0 : 1;
}

int TextCommands::CmdHello(int argc, char** argv) {
    return SendMessageWithStatus("你好小智", "向小智问好") ? 0 : 1;
}

int TextCommands::CmdWeather(int argc, char** argv) {
    std::string city = (argc > 1) ? argv[1] : "当前位置";
    std::string message = city + "的天气怎么样";
    
    char action[64];
    snprintf(action, sizeof(action), "查询%s天气", city.c_str());
    
    return SendMessageWithStatus(message, action) ? 0 : 1;
}

int TextCommands::CmdTime(int argc, char** argv) {
    return SendMessageWithStatus("现在几点了", "询问当前时间") ? 0 : 1;
}

int TextCommands::CmdJoke(int argc, char** argv) {
    return SendMessageWithStatus("给我讲个笑话", "请小智讲笑话") ? 0 : 1;
}

int TextCommands::CmdTextHelp(int argc, char** argv) {
    printf("\n=== 小智文本交互命令 ===\n");
    printf("ask <message>  - 发送任意文本给小智\n");
    printf("hello          - 向小智问好\n");
    printf("weather [city] - 查询天气 (默认当前位置)\n");
    printf("time           - 询问当前时间\n");
    printf("joke           - 请小智讲个笑话\n");
    printf("texthelp       - 显示此帮助信息\n");
    printf("textstatus     - 显示文本交互系统状态\n");
    printf("\n使用示例:\n");
    printf("  ask 今天天气怎么样\n");
    printf("  weather 北京\n");
    printf("  hello\n");
    printf("========================\n\n");
    return 0;
}

int TextCommands::CmdTextStatus(int argc, char** argv) {
    if (!manager_) {
        printf("❌ 错误: 文本交互管理器未初始化\n");
        return 1;
    }

    printf("\n=== 文本交互系统状态 ===\n");
    printf("%s\n", manager_->GetStatusDescription().c_str());
    printf("========================\n\n");
    return 0;
}

// ========== 辅助函数实现 ==========

bool TextCommands::BuildMessageFromArgs(int argc, char** argv, int start_index, std::string& message) {
    if (argc <= start_index) {
        return false;
    }

    message.clear();
    for (int i = start_index; i < argc; i++) {
        if (i > start_index) {
            message += " ";
        }
        message += argv[i];
    }

    return !message.empty();
}

void TextCommands::PrintUsage(const char* command, const char* usage, const char* example) {
    printf("用法: %s\n", usage);
    if (example) {
        printf("示例: %s\n", example);
    }
}

bool TextCommands::SendMessageWithStatus(const std::string& message, const char* action_description) {
    if (!manager_) {
        printf("❌ 错误: 文本交互管理器未初始化\n");
        return false;
    }

    printf("🚀 %s...\n", action_description);
    return manager_->SendTextToXiaozhi(message);
}

} // namespace text_interaction

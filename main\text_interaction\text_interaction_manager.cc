#include "text_interaction_manager.h"
#include "application.h"
#include "protocols/protocol.h"
#include "esp_log.h"
#include <cstdio>

static const char* TAG = "TextInteractionManager";

namespace text_interaction {

TextInteractionManager::TextInteractionManager(Application& app)
    : app_(app)
    , text_interaction_mode_(false)
    , initialized_(false)
    , total_messages_sent_(0)
    , successful_messages_(0)
    , failed_messages_(0) {
}

bool TextInteractionManager::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "TextInteractionManager already initialized");
        return true;
    }

    ESP_LOGI(TAG, "Initializing TextInteractionManager...");
    
    // 重置统计信息
    total_messages_sent_ = 0;
    successful_messages_ = 0;
    failed_messages_ = 0;
    text_interaction_mode_ = false;
    
    initialized_ = true;
    ESP_LOGI(TAG, "TextInteractionManager initialized successfully");
    
    return true;
}

bool TextInteractionManager::SendTextTo<PERSON><PERSON><PERSON><PERSON>(const std::string& text) {
    if (!initialized_) {
        ESP_LOGE(TAG, "TextInteractionManager not initialized");
        printf("❌ 错误: 文本交互管理器未初始化\n");
        return false;
    }

    if (text.empty()) {
        ESP_LOGW(TAG, "Empty text message");
        printf("❌ 错误: 文本内容为空\n");
        return false;
    }

    ESP_LOGI(TAG, "发送文本给小智: %s", text.c_str());
    printf("📤 发送消息给小智: %s\n", text.c_str());
    
    total_messages_sent_++;

    // 检查协议是否可用
    if (!IsProtocolReady()) {
        ESP_LOGE(TAG, "Protocol not ready");
        printf("❌ 错误: 协议未就绪\n");
        failed_messages_++;
        LogSendStatus(text, false);
        return false;
    }

    // 使用应用程序的调度器来执行异步操作
    app_.Schedule([this, text]() {
        // 强制建立连接（如果需要）
        if (!ForceConnectIfNeeded()) {
            printf("❌ 错误: 无法连接到服务器\n");
            failed_messages_++;
            LogSendStatus(text, false);
            return;
        }

        // 设置文本交互模式
        SetTextInteractionMode(true);

        // 获取协议实例并发送消息
        auto protocol = app_.GetProtocol();
        if (protocol) {
            protocol->SendWakeWordDetected(text);
            successful_messages_++;
            printf("✅ 消息已发送: %s\n", text.c_str());
            printf("🎵 等待小智语音回复...\n");
            LogSendStatus(text, true);
        } else {
            printf("❌ 错误: 协议实例无效\n");
            failed_messages_++;
            LogSendStatus(text, false);
            SetTextInteractionMode(false);
        }
    });

    return true;
}

bool TextInteractionManager::IsInTextInteractionMode() const {
    return text_interaction_mode_;
}

void TextInteractionManager::SetTextInteractionMode(bool enabled) {
    if (text_interaction_mode_ != enabled) {
        text_interaction_mode_ = enabled;
        ESP_LOGI(TAG, "Text interaction mode: %s", enabled ? "ENABLED" : "DISABLED");
    }
}

void TextInteractionManager::OnTtsStop() {
    if (text_interaction_mode_) {
        ESP_LOGI(TAG, "TTS stopped in text interaction mode, returning to idle");
        printf("🎉 小智回复完成\n");
        SetTextInteractionMode(false);
    }
}

std::string TextInteractionManager::GetStatusDescription() const {
    char buffer[256];
    snprintf(buffer, sizeof(buffer),
        "文本交互状态:\n"
        "  初始化: %s\n"
        "  交互模式: %s\n"
        "  总消息数: %zu\n"
        "  成功: %zu\n"
        "  失败: %zu\n"
        "  成功率: %.1f%%",
        initialized_ ? "是" : "否",
        text_interaction_mode_ ? "启用" : "禁用",
        total_messages_sent_,
        successful_messages_,
        failed_messages_,
        total_messages_sent_ > 0 ? (successful_messages_ * 100.0 / total_messages_sent_) : 0.0
    );
    return std::string(buffer);
}

bool TextInteractionManager::ForceConnectIfNeeded() {
    auto protocol = app_.GetProtocol();
    if (!protocol) {
        ESP_LOGE(TAG, "Protocol instance is null");
        return false;
    }

    // 如果音频通道未打开，强制打开
    if (!protocol->IsAudioChannelOpened()) {
        ESP_LOGI(TAG, "音频通道未打开，正在连接...");
        printf("🔗 正在连接到小智服务器...\n");

        // 获取当前设备状态
        auto current_state = app_.GetDeviceState();
        
        // 临时设置为连接状态
        app_.SetDeviceState(kDeviceStateConnecting);

        // 尝试打开音频通道
        if (!protocol->OpenAudioChannel()) {
            ESP_LOGE(TAG, "Failed to open audio channel");
            app_.SetDeviceState(current_state);  // 恢复原状态
            return false;
        }

        // 连接成功，设置为监听状态以接收响应
        app_.SetDeviceState(kDeviceStateListening);
        ESP_LOGI(TAG, "音频通道已打开，准备接收响应");
        printf("✅ 已连接到小智服务器\n");
    }

    return true;
}

bool TextInteractionManager::IsProtocolReady() const {
    auto protocol = app_.GetProtocol();
    return protocol != nullptr;
}

void TextInteractionManager::LogSendStatus(const std::string& text, bool success) const {
    if (success) {
        ESP_LOGI(TAG, "Successfully sent text: %s", text.c_str());
    } else {
        ESP_LOGE(TAG, "Failed to send text: %s", text.c_str());
    }
}

} // namespace text_interaction

@echo off
setlocal EnableDelayedExpansion
title ESP32-S3 Quick Build Flash

echo ================================================
echo ESP32-S3 Quick Build Mode
echo ================================================
echo Features: Keep build cache, suitable for small changes
echo Estimated time: 1-3 minutes
echo Start time: %time%
echo ================================================
echo.

cd /d "J:\xiaozhi-esp32"
echo Working directory: %CD%

REM Environment setup
call :setup_environment

REM Quick compile (keep cache)
call :quick_compile

REM Check build result
if %ERRORLEVEL% NEQ 0 (
    echo Quick build failed, error code: %ERRORLEVEL%
    goto :end
)

REM Flash firmware
call :flash_firmware

REM Check flash result
if %ERRORLEVEL% NEQ 0 (
    echo Flash failed, please check COM13 port connection
    goto :end
)

REM Start monitoring
call :start_monitoring

goto :end

:setup_environment
echo.
echo ================================================
echo Environment Setup Phase
echo ================================================
echo AI_MONITOR_PHASE: ENVIRONMENT_SETUP
echo AI_MONITOR_TIME: %time%

set IDF_PATH=D:\Espressif\frameworks\esp-idf-v5.4.1
set IDF_TOOLS_PATH=D:\Espressif
set IDF_PYTHON_ENV_PATH=D:\Espressif\python_env\idf5.4_py3.11_env
set PYTHON=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe

set PATH=D:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin;%PATH%
set PATH=D:\Espressif\tools\cmake\3.30.2\bin;%PATH%
set PATH=D:\Espressif\tools\ninja\1.12.1;%PATH%
set PATH=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts;%PATH%
set PATH=D:\Espressif\tools\idf-git\2.44.0\cmd;%PATH%

echo ESP-IDF v5.4.1 environment configured
echo AI_MONITOR_RESULT: ENVIRONMENT_READY
goto :eof

:quick_compile
echo.
echo ================================================
echo PHASE_START: QUICK_COMPILATION
echo ================================================
echo AI_MONITOR_PHASE: QUICK_BUILD_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: Incremental build, keep cache
echo AI_MONITOR_DURATION: Estimated 1-3 minutes
echo ================================================
echo.

echo Starting quick build...
%PYTHON% %IDF_PATH%\tools\idf.py build

echo.
echo ================================================
echo PHASE_END: QUICK_COMPILATION
echo ================================================
if %ERRORLEVEL% EQU 0 (
    echo AI_MONITOR_RESULT: QUICK_BUILD_SUCCESS
    echo AI_MONITOR_NEXT: FLASH_PHASE_STARTING
    echo Quick build successful
) else (
    echo AI_MONITOR_RESULT: QUICK_BUILD_FAILED
    echo AI_MONITOR_ERROR_CODE: %ERRORLEVEL%
    echo Quick build failed, error code: %ERRORLEVEL%
)
echo AI_MONITOR_TIME: %time%
echo ================================================
goto :eof

:flash_firmware
echo.
echo ================================================
echo PHASE_START: FLASHING
echo ================================================
echo AI_MONITOR_PHASE: FLASH_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: Flash progress and verification
echo AI_MONITOR_DURATION: Estimated 30 seconds to 2 minutes
echo AI_MONITOR_TARGET: COM13 port
echo ================================================
echo.

echo Starting flash to COM13...
%PYTHON% %IDF_PATH%\tools\idf.py -p COM13 flash

echo.
echo ================================================
echo PHASE_END: FLASHING
echo ================================================
if %ERRORLEVEL% EQU 0 (
    echo AI_MONITOR_RESULT: FLASH_SUCCESS
    echo AI_MONITOR_NEXT: MONITOR_PHASE_STARTING
    echo Flash successful
) else (
    echo AI_MONITOR_RESULT: FLASH_FAILED
    echo AI_MONITOR_ERROR_CODE: %ERRORLEVEL%
    echo Flash failed, error code: %ERRORLEVEL%
)
echo AI_MONITOR_TIME: %time%
echo ================================================
goto :eof

:start_monitoring
echo.
echo ================================================
echo PHASE_START: ESP32_MONITORING
echo ================================================
echo AI_MONITOR_PHASE: ESP32_RUNTIME_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: System startup logs and Hello XiaoZhi
echo AI_MONITOR_DURATION: At least 2 minutes
echo AI_MONITOR_SUCCESS_SIGNAL: Hello XiaoZhi
echo ================================================
echo.
echo Key monitoring points:
echo    - WiFi connection success
echo    - Network console started
echo    - TTS system initialized
echo    - Voice wake-up ready
echo    - BLE GATT server started
echo    - System startup complete: Hello XiaoZhi
echo.
echo AI_MONITOR_START: ESP32_LOGS_BEGIN
echo Starting ESP32 monitoring...

%PYTHON% %IDF_PATH%\tools\idf.py -p COM13 monitor

echo AI_MONITOR_END: ESP32_LOGS_END
goto :eof

:end
echo.
echo ================================================
echo Quick Build Flash Process Complete
echo ================================================
echo End time: %time%
echo AI_MONITOR_PHASE: QUICK_BUILD_COMPLETE

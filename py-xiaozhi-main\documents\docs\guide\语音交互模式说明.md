# 语音交互模式说明

![Image](./images/系统界面.png)

## 项目概述

py-xiaozhi是一个基于Python开发的AI语音助手客户端，采用现代化的异步架构设计，支持丰富的多模态交互功能。系统集成了语音识别、自然语言处理、视觉识别、IoT设备控制等先进技术，为用户提供智能化的交互体验。

### 核心特性
- **多协议支持**: WebSocket/MQTT双协议通信
- **MCP工具生态**: 集成10+专业工具模块
- **IoT设备集成**: Thing-based架构的设备管理
- **视觉识别**: 基于GLM-4V的多模态理解
- **音频处理**: Opus编码+WebRTC增强
- **全局快捷键**: 系统级交互控制

## 语音交互模式

系统提供多种语音交互方式，支持灵活的交互控制和智能语音检测：

### 1. 手动按压模式

- **操作方法**: 按住快捷键期间录音，松开自动发送
- **默认快捷键**: `Ctrl+J` (可在配置中修改)
- **适用场景**: 精确控制录音时间，避免环境噪音干扰
- **优势特点**: 
  - 避免误触发录音
  - 录音时长完全可控
  - 适合嘈杂环境使用

### 2. 回合制模式

- **操作方法**: 按下快捷键开启/点击GUI右下角手动对话模式切换自动对话
- **默认快捷键**: `Ctrl+K` (可在配置中修改)
- **适用场景**: 连续对话、长时间交互
- **智能特性**: 支持连续多轮对话

### 3. 唤醒词模式

- **操作方法**: 语音说出预设唤醒词激活系统
- **默认唤醒词**: "小智"、"小美" (可在配置中自定义)
- **模型支持**: 基于Vosk离线语音识别
- **配置要求**: 需要下载对应的语音识别模型

### 模式切换与配置

```json
// 在 config/config.json 中配置快捷键
{
  "SHORTCUTS": {
    "ENABLED": true,
    "MANUAL_PRESS": {"modifier": "ctrl", "key": "j"},
    "AUTO_TOGGLE": {"modifier": "ctrl", "key": "k"},
    "MODE_TOGGLE": {"modifier": "ctrl", "key": "m"}
  }
}
```

- **界面显示**: GUI右下角实时显示当前交互模式
- **快速切换**: 使用 `Ctrl+M` 在不同模式间快速切换
- **状态指示**: 系统托盘图标颜色反映当前状态

## 对话控制与系统状态

### 智能打断功能

当AI正在语音回复时，用户可以随时中断对话：

- **快捷键打断**: `Ctrl+Q` - 立即停止当前AI回复
- **GUI操作**: 点击界面上的"中断"按钮
- **智能检测**: 系统检测到新的语音输入时自动中断回复

### 系统状态管理

系统采用事件驱动的状态机架构，具有以下运行状态：

```
┌─────────────────────────────────────────────────────────┐
│                    系统状态流转图                          │
└─────────────────────────────────────────────────────────┘

     IDLE              CONNECTING           LISTENING
  ┌─────────┐    唤醒词/按钮   ┌─────────┐  连接成功  ┌─────────┐
  │  空闲   │  ─────────────> │  连接中  │ ────────> │  聆听中  │
  │  待命   │                │  服务器  │           │  录音中  │
  └─────────┘                └─────────┘           └─────────┘
       ↑                           │                     │
       │                         连接失败                 │ 语音识别
       │                           │                     │ 完成/超时
       │                           ↓                     │
       │                     ┌─────────┐                 │
       └──── 播放完成/中断 ──── │  回复中  │ <──────────────┘
                             │  AI说话  │
                             └─────────┘
```

### 状态指示说明

**系统托盘图标颜色**:
- **绿色**: 系统正常运行，处于待命状态
- **黄色**: 正在聆听用户语音输入
- **蓝色**: AI正在语音回复中
- **红色**: 系统错误或连接异常
- **灰色**: 未连接到服务器

## 快捷键系统

系统提供丰富的全局快捷键支持，详细说明请参考：[快捷键说明](./快捷键说明.md)

### 常用快捷键

| 快捷键 | 功能描述 | 备注 |
|--------|----------|------|
| `Ctrl+J` | 按住说话模式 | 按住期间录音，松开发送 |
| `Ctrl+K` | 自动对话模式 | 开启/关闭自动语音检测 |
| `Ctrl+Q` | 中断对话 | 立即停止AI回复 |
| `Ctrl+M` | 切换交互模式 | 在手动/自动模式间切换 |
| `Ctrl+W` | 显示/隐藏窗口 | 窗口最小化/还原 |

## 智能语音命令系统

### 基础交互命令
- **问候语**: "你好"、"你是谁"、"早上好"
- **礼貌用语**: "谢谢"、"再见"、"请帮助我"
- **状态询问**: "系统状态如何"、"连接正常吗"

### 视觉识别命令

集成GLM-4V多模态理解能力：

```bash
# 视觉识别分析
"识别画面"             # 分析当前摄像头画面
"看看摄像头前有什么"    # 描述看到的内容
"这是什么东西"         # 物体识别
```

### MCP工具调用命令

利用丰富的MCP工具生态：

```bash
# 日历管理
"创建明天下午3点的会议提醒"
"查看今天的日程安排"

# 定时器功能  
"一分钟后播放菊花台"

# 系统操作
"查看系统信息"
"调节音量到80%"

# 网络搜索
"搜索今天的天气"
"查找最近热点"

# 地图导航
"查找附近的咖啡店"
"导航到北京天安门"

# 美食菜谱
"推荐今天晚餐菜谱"
"教我做宫保鸡丁"

# 八字命理(可选)
"分析我的生辰八字"
"今天运势如何"
```

## 运行模式与部署

### GUI模式 (默认)

图形用户界面模式，提供直观的交互体验：

```bash
# 标准启动
python main.py

# 使用MQTT协议
python main.py --protocol mqtt
```

**GUI模式特性**:
- 可视化操作界面
- 实时状态显示
- 音频波形可视化
- 系统托盘支持
- 图形化设置界面

### CLI模式

命令行界面模式，适合服务器部署：

```bash
# CLI模式启动
python main.py --mode cli

# CLI + MQTT协议
python main.py --mode cli --protocol mqtt
```

**CLI模式特性**:
- 低资源占用
- 服务器友好
- 详细日志输出
- 键盘快捷键支持
- 脚本化部署

**构建特性**:
- 跨平台支持
- 单文件模式
- 依赖打包
- 自动化配置

## 平台兼容性

### Windows 平台
- **完全兼容**: 所有功能正常支持
- **音频增强**: 支持Windows音频API
- **音量控制**: 集成pycaw音量管理
- **系统托盘**: 完整托盘功能支持
- **全局热键**: 完整快捷键功能

### macOS 平台  
- **完全兼容**: 核心功能完整支持
- **状态栏**: 托盘图标显示在顶部状态栏
- **权限管理**: 可能需要授权麦克风/摄像头权限
- **快捷键**: 部分快捷键需要系统权限
- **音频**: 原生CoreAudio支持

### Linux 平台
- **兼容性**: 支持主流发行版(Ubuntu/CentOS/Debian)
- **桌面环境**: 
  - GNOME: 完整支持
  - KDE: 完整支持  
  - Xfce: 需要额外托盘支持
- **音频系统**:
  - PulseAudio: 推荐(自动检测)
  - ALSA: 备用方案
- **依赖**: 可能需要安装系统托盘支持包

```bash
# Ubuntu/Debian 托盘支持
sudo apt-get install libappindicator3-1

# CentOS/RHEL 托盘支持  
sudo yum install libappindicator-gtk3
```

## 故障排除指南

### 常见问题

**1. 语音识别不工作**
- 使用小美、小明等简易识别的唤醒词

**2. 摄像头无法使用** 
```bash
# 测试摄像头
python scripts/camera_scanner.py

# 检查摄像头权限和设备索引
```

**3. 快捷键不响应**
- 检查是否有其他程序占用相同快捷键
- 尝试以管理员权限运行(Windows)
- 检查系统安全软件拦截

**4. 网络连接问题**
- 检查防火墙设置
- 验证WebSocket/MQTT服务器地址
- 测试网络连通性
#!/usr/bin/env python3
"""
P3文件格式检查工具
检查P3文件的格式、参数和数据完整性
"""

import struct
import sys
import os

def check_p3_file(filepath):
    """检查P3文件格式"""
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    file_size = os.path.getsize(filepath)
    print(f"📁 文件: {filepath}")
    print(f"📊 文件大小: {file_size:,} bytes")
    
    try:
        with open(filepath, 'rb') as f:
            frame_count = 0
            total_opus_data = 0
            
            print("\n🔍 P3格式分析:")
            print("=" * 50)
            
            while True:
                # 读取4字节头部
                header = f.read(4)
                if len(header) < 4:
                    break
                
                # 解析头部: [1字节类型, 1字节保留, 2字节长度]
                packet_type, reserved, data_len = struct.unpack('>BBH', header)
                
                if frame_count < 5:  # 只显示前5帧的详细信息
                    print(f"帧 {frame_count + 1}:")
                    print(f"  类型: {packet_type} (应为0)")
                    print(f"  保留: {reserved} (应为0)")
                    print(f"  数据长度: {data_len} bytes")
                
                # 读取Opus数据
                opus_data = f.read(data_len)
                if len(opus_data) != data_len:
                    print(f"❌ 帧 {frame_count + 1}: Opus数据不完整 (期望{data_len}, 实际{len(opus_data)})")
                    break
                
                if frame_count < 5:
                    print(f"  Opus数据: {len(opus_data)} bytes")
                    if len(opus_data) >= 8:
                        print(f"  数据头部: {opus_data[:8].hex().upper()}")
                    print()
                
                frame_count += 1
                total_opus_data += data_len
                
                # 验证格式
                if packet_type != 0:
                    print(f"⚠️  帧 {frame_count}: 类型字段异常 ({packet_type})")
                if reserved != 0:
                    print(f"⚠️  帧 {frame_count}: 保留字段异常 ({reserved})")
            
            print("📈 统计信息:")
            print(f"  总帧数: {frame_count}")
            print(f"  总Opus数据: {total_opus_data:,} bytes")
            print(f"  头部开销: {frame_count * 4:,} bytes")
            print(f"  数据效率: {total_opus_data / file_size * 100:.1f}%")
            
            # 计算音频时长 (每帧60ms)
            duration_ms = frame_count * 60
            duration_sec = duration_ms / 1000
            print(f"  预计时长: {duration_sec:.1f}秒 ({duration_ms}ms)")
            
            # 验证P3格式规范
            print("\n✅ 格式验证:")
            if frame_count > 0:
                print("  ✓ P3格式头部正确")
                print("  ✓ 采样率: 16000Hz (固定)")
                print("  ✓ 声道: 单声道 (固定)")
                print("  ✓ 帧时长: 60ms (固定)")
                print("  ✓ 编码: Opus")
                return True
            else:
                print("  ❌ 无有效P3帧")
                return False
                
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_p3_format.py <p3文件路径>")
        print("示例: python check_p3_format.py audio_files/001.p3")
        return 1
    
    filepath = sys.argv[1]
    success = check_p3_file(filepath)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="pywin32" />
            <item index="1" class="java.lang.String" itemvalue="PyQt6" />
            <item index="2" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="3" class="java.lang.String" itemvalue="pywinauto" />
            <item index="4" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="5" class="java.lang.String" itemvalue="pillow" />
            <item index="6" class="java.lang.String" itemvalue="comtypes" />
            <item index="7" class="java.lang.String" itemvalue="psutil" />
            <item index="8" class="java.lang.String" itemvalue="protobuf" />
            <item index="9" class="java.lang.String" itemvalue="chardet" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>
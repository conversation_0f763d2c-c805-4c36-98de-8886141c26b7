# 定时器工具 (Timer Tools)

定时器工具是一个功能强大的 MCP 倒计时工具集，提供了定时任务创建、管理、取消等功能，支持延迟执行各种操作。

### 常见使用场景

**定时提醒:**
- "5分钟后提醒我开会"
- "10分钟后提醒我休息"
- "半小时后提醒我吃药"
- "1小时后提醒我关电脑"

**定时播放:**
- "5分钟后播放音乐"
- "10分钟后播放我喜欢的歌"
- "15分钟后停止播放"
- "20分钟后播放闹铃"

**定时系统操作:**
- "30分钟后调低音量"
- "1小时后关闭系统"
- "10分钟后检查系统状态"
- "5分钟后查看天气"

**定时查询:**
- "5分钟后查询火车票"
- "10分钟后搜索新闻"
- "15分钟后查看日程"
- "20分钟后查询菜谱"

**任务管理:**
- "查看当前有哪些定时任务"
- "取消刚才设置的定时器"
- "查看所有活动的定时器"
- "取消5分钟后的提醒"

### 使用提示

1. **时间表达**: 支持多种时间表达方式，如"5分钟后"、"半小时后"、"1小时后"
2. **任务描述**: 可以添加任务描述帮助识别不同的定时任务
3. **任务管理**: 可以查看和取消正在运行的定时任务
4. **灵活设置**: 支持设置各种类型的延迟执行任务

AI 助手会根据您的需求自动调用定时器工具，为您提供便捷的定时任务管理服务。

## 功能概览

### 倒计时功能
- **定时执行**: 延迟指定时间后执行任务
- **任务类型**: 支持各种MCP工具调用
- **时间设置**: 灵活的时间设置选项
- **任务描述**: 可添加任务描述便于管理

### 任务管理功能
- **任务创建**: 创建新的定时任务
- **任务取消**: 取消正在运行的定时任务
- **任务查询**: 查看所有活动的定时任务
- **状态监控**: 实时监控任务执行状态

### 执行控制功能
- **延迟控制**: 精确控制执行延迟时间
- **任务队列**: 管理多个并发定时任务
- **错误处理**: 完善的错误处理机制
- **日志记录**: 详细的任务执行日志

### 系统集成功能
- **MCP集成**: 与其他MCP工具无缝集成
- **异步执行**: 支持异步任务执行
- **资源管理**: 合理管理系统资源
- **性能优化**: 优化定时任务性能

## 工具列表

### 1. 定时任务工具

#### start_countdown_timer - 启动倒计时任务
创建并启动一个倒计时任务，在指定时间后执行指定的操作。

**参数:**
- `command` (必需): 要执行的MCP工具调用，JSON格式字符串
- `delay` (可选): 延迟时间（秒），默认5秒
- `description` (可选): 任务描述

**使用场景:**
- 定时提醒
- 延迟执行任务
- 定时播放音乐
- 定时系统操作

#### cancel_countdown_timer - 取消倒计时任务
取消指定的正在运行的倒计时任务。

**参数:**
- `timer_id` (必需): 要取消的计时器ID

**使用场景:**
- 取消不需要的定时任务
- 修改定时任务设置
- 清理定时任务

#### get_active_countdown_timers - 获取活动定时器
获取所有正在运行的倒计时任务状态。

**参数:**
无

**使用场景:**
- 查看当前定时任务
- 管理定时任务
- 监控任务状态

## 使用示例

### 定时任务创建示例

```python
# 创建5分钟后的提醒任务
result = await mcp_server.call_tool("start_countdown_timer", {
    "command": '{"name": "create_event", "arguments": {"title": "会议提醒", "start_time": "2024-01-15T14:00:00"}}',
    "delay": 300,
    "description": "会议提醒"
})

# 创建10分钟后播放音乐的任务
result = await mcp_server.call_tool("start_countdown_timer", {
    "command": '{"name": "search_and_play", "arguments": {"song_name": "轻音乐"}}',
    "delay": 600,
    "description": "播放轻音乐"
})

# 创建30分钟后调整音量的任务
result = await mcp_server.call_tool("start_countdown_timer", {
    "command": '{"name": "set_volume", "arguments": {"volume": 30}}',
    "delay": 1800,
    "description": "调低音量"
})
```

### 任务管理示例

```python
# 查看所有活动的定时任务
result = await mcp_server.call_tool("get_active_countdown_timers", {})

# 取消指定的定时任务
result = await mcp_server.call_tool("cancel_countdown_timer", {
    "timer_id": "timer_123"
})
```

## 数据结构

### 倒计时任务 (CountdownTimer)
```python
{
    "timer_id": "timer_123",
    "command": {
        "name": "create_event",
        "arguments": {
            "title": "会议提醒",
            "start_time": "2024-01-15T14:00:00"
        }
    },
    "delay": 300,
    "description": "会议提醒",
    "created_at": "2024-01-15T10:25:00Z",
    "execute_at": "2024-01-15T10:30:00Z",
    "status": "running",
    "remaining_time": 240
}
```

### 任务创建响应 (CreateResponse)
```python
{
    "success": true,
    "message": "倒计时任务创建成功",
    "timer_id": "timer_123",
    "execute_at": "2024-01-15T10:30:00Z",
    "remaining_time": 300,
    "description": "会议提醒"
}
```

### 任务取消响应 (CancelResponse)
```python
{
    "success": true,
    "message": "倒计时任务已取消",
    "timer_id": "timer_123",
    "cancelled_at": "2024-01-15T10:27:00Z"
}
```

### 活动任务列表 (ActiveTimers)
```python
{
    "success": true,
    "total_active_timers": 2,
    "timers": [
        {
            "timer_id": "timer_123",
            "description": "会议提醒",
            "remaining_time": 240,
            "execute_at": "2024-01-15T10:30:00Z",
            "status": "running"
        },
        {
            "timer_id": "timer_456",
            "description": "播放音乐",
            "remaining_time": 480,
            "execute_at": "2024-01-15T10:33:00Z",
            "status": "running"
        }
    ]
}
```

## 任务状态说明

### 任务状态类型
- **running**: 正在运行，等待执行
- **executing**: 正在执行任务
- **completed**: 已完成执行
- **cancelled**: 已被取消
- **failed**: 执行失败

### 时间相关字段
- **created_at**: 任务创建时间
- **execute_at**: 任务执行时间
- **remaining_time**: 剩余时间（秒）
- **cancelled_at**: 任务取消时间
- **completed_at**: 任务完成时间

## 支持的命令类型

### 日程管理命令
- **create_event**: 创建日程事件
- **update_event**: 更新日程事件
- **delete_event**: 删除日程事件

### 音乐播放命令
- **search_and_play**: 搜索并播放音乐
- **play_pause**: 播放/暂停音乐
- **stop**: 停止播放
- **get_status**: 获取播放状态

### 系统控制命令
- **set_volume**: 设置音量
- **get_system_status**: 获取系统状态

### 搜索查询命令
- **search_bing**: 网络搜索
- **query_train_tickets**: 查询火车票
- **get_recipe_by_id**: 获取菜谱

## 时间设置规范

### 时间单位
- **秒**: 最小时间单位
- **分钟**: 60秒
- **小时**: 3600秒
- **天**: 86400秒

### 常用时间设置
- **5分钟**: 300秒
- **10分钟**: 600秒
- **15分钟**: 900秒
- **30分钟**: 1800秒
- **1小时**: 3600秒
- **2小时**: 7200秒

### 时间限制
- **最小延迟**: 1秒
- **最大延迟**: 24小时（86400秒）
- **推荐范围**: 1秒 - 4小时

## 最佳实践

### 1. 任务设计
- 使用清晰的任务描述
- 设置合理的延迟时间
- 选择适当的执行命令
- 考虑任务执行的时机

### 2. 任务管理
- 定期检查活动任务
- 及时取消不需要的任务
- 避免创建过多并发任务
- 合理安排任务时间

### 3. 错误处理
- 验证命令格式正确性
- 处理任务执行失败情况
- 监控任务执行状态
- 记录任务执行日志

### 4. 性能优化
- 避免创建过短间隔的任务
- 合理控制并发任务数量
- 优化任务执行逻辑
- 定期清理完成的任务

## 使用场景示例

### 工作效率场景
1. **番茄工作法**: 25分钟后提醒休息
2. **会议提醒**: 会议前5分钟提醒准备
3. **任务切换**: 1小时后切换到下一个任务
4. **定时检查**: 每30分钟检查邮件

### 生活助手场景
1. **烹饪定时**: 10分钟后提醒查看炉灶
2. **服药提醒**: 每8小时提醒服药
3. **运动提醒**: 每小时提醒起身活动
4. **睡眠提醒**: 晚上10点提醒准备睡觉

### 娱乐场景
1. **音乐播放**: 30分钟后播放睡前音乐
2. **游戏提醒**: 1小时后提醒休息
3. **视频定时**: 2小时后暂停视频
4. **阅读提醒**: 45分钟后提醒休息眼睛

## 注意事项

1. **时间准确性**: 定时器基于系统时间，确保系统时间准确
2. **任务复杂性**: 避免在定时任务中执行过于复杂的操作
3. **资源管理**: 合理控制并发任务数量，避免资源过度占用
4. **错误恢复**: 任务执行失败时系统会自动清理
5. **任务持久性**: 系统重启后定时任务会丢失

## 故障排除

### 常见问题
1. **任务创建失败**: 检查命令格式和参数
2. **任务执行失败**: 检查目标工具是否可用
3. **任务取消失败**: 确认任务ID是否正确
4. **时间设置错误**: 验证时间参数范围

### 调试方法
1. 查看任务创建返回的错误信息
2. 检查活动任务列表确认任务状态
3. 验证命令格式是否正确
4. 测试目标工具是否正常工作

### 性能优化建议
1. 避免创建过多短时间间隔的任务
2. 合理设置任务描述便于管理
3. 定期清理不需要的任务
4. 监控系统资源使用情况

通过定时器工具，您可以轻松设置各种定时任务，提高工作效率和生活便利性。
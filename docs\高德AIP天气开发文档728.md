# ESP32-S3汽车电子项目高德天气API开发指南

## 📋 文档概述

**文档版本**: V1.0  
**创建日期**: 2025年7月28日  
**适用平台**: ESP32-S3  
**目标读者**: 嵌入式开发工程师  

本指南提供ESP32-S3汽车电子项目中高德天气API集成的完整移植方案，包含定位服务、天气数据获取、智能播报等技术细节。

---

## 🎯 功能概述

### 核心功能
- **IP定位服务**: 基于高德IP定位API的自动位置获取
- **天气数据获取**: 实时天气信息查询和解析
- **智能播报**: 结合天气状况的智能语音提醒
- **缓存机制**: NVS存储减少API调用频率

### 系统架构
```mermaid
graph TB
    A[启动触发] --> B[高德天气客户端]
    B --> C[IP定位服务]
    C --> D[获取区县编码]
    D --> E[天气信息查询]
    E --> F[JSON数据解析]
    F --> G[天气数据缓存]
    G --> H[智能播报生成]
    H --> I[TTS语音播报]
    
    J[NVS缓存] --> E
    K[手动区县设置] --> D
    L[网络连接检查] --> C
```

---

## 🔌 1. 高德API配置

### 1.1 API密钥配置

```c
// 高德API配置
#define AMAP_API_KEY                "59826e89815022b655bcc7b1ecb4504f"
#define AMAP_WEATHER_URL            "http://restapi.amap.com/v3/weather/weatherInfo"
#define AMAP_IP_LOCATION_URL        "http://restapi.amap.com/v3/ip"

// 网络配置
#define WEATHER_HTTP_TIMEOUT_MS     10000   // 10秒超时
#define WEATHER_MAX_RESPONSE_SIZE   1024    // 1KB响应大小
#define WEATHER_MAX_RETRY_COUNT     3       // 最大重试次数
```

### 1.2 深圳各区adcode配置

```c
// 深圳各区adcode定义
#define SHENZHEN_LUOHU_ADCODE       "440303"  // 罗湖区
#define SHENZHEN_FUTIAN_ADCODE      "440304"  // 福田区
#define SHENZHEN_NANSHAN_ADCODE     "440305"  // 南山区
#define SHENZHEN_BAOAN_ADCODE       "440306"  // 宝安区
#define SHENZHEN_LONGGANG_ADCODE    "440307"  // 龙岗区
#define SHENZHEN_YANTIAN_ADCODE     "440308"  // 盐田区

// 默认使用龙岗区
#define DEFAULT_DISTRICT_ADCODE     SHENZHEN_LONGGANG_ADCODE
```

### 1.3 缓存配置

```c
// 缓存配置
#define WEATHER_CACHE_DURATION_MS   1800000 // 30分钟缓存时间
#define WEATHER_NVS_NAMESPACE       "weather"
#define WEATHER_NVS_KEY_CITY        "city"
#define WEATHER_NVS_KEY_ADCODE      "adcode"
#define WEATHER_NVS_KEY_WEATHER     "weather_data"
#define WEATHER_NVS_KEY_TIMESTAMP   "timestamp"
#define WEATHER_NVS_KEY_MANUAL_ADCODE "manual_adcode"  // 手动设置的区县编码
```

---

## 🌍 2. 数据结构定义

### 2.1 位置信息结构

```c
/**
 * @brief 位置信息结构体
 */
typedef struct {
    char province[64];      // 省份
    char city[64];          // 城市
    char district[64];      // 区县
    char adcode[16];        // 城市编码
    char rectangle[128];    // 所在城市矩形区域范围
} location_info_t;
```

### 2.2 天气信息结构

```c
/**
 * @brief 天气信息结构体
 */
typedef struct {
    char province[64];      // 省份
    char city[64];          // 城市
    char adcode[16];        // 区域编码
    char weather[32];       // 天气现象
    char temperature[16];   // 实时气温
    char winddirection[32]; // 风向描述
    char windpower[16];     // 风力级别
    char humidity[16];      // 空气湿度
    char reporttime[32];    // 数据发布时间
    uint64_t cache_time;    // 缓存时间戳
} weather_info_t;
```

### 2.3 错误码定义

```c
/**
 * @brief 天气系统错误码
 */
typedef enum {
    WEATHER_OK = 0,                 // 成功
    WEATHER_ERR_INVALID_PARAM,      // 参数错误
    WEATHER_ERR_NETWORK,            // 网络错误
    WEATHER_ERR_API_LIMIT,          // API限制
    WEATHER_ERR_PARSE_FAILED,       // 解析失败
    WEATHER_ERR_CACHE_FAILED,       // 缓存失败
    WEATHER_ERR_NO_DATA,            // 无数据
    WEATHER_ERR_TIMEOUT             // 超时
} weather_error_t;
```

---

## 🌐 3. HTTP请求处理

### 3.1 HTTP事件处理

```c
/**
 * @brief HTTP事件处理函数
 */
static esp_err_t http_event_handler(esp_http_client_event_t *evt) {
    http_response_data_t *response = (http_response_data_t *)evt->user_data;
    
    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            // 检查缓冲区容量
            if (response->size + evt->data_len >= response->capacity) {
                ESP_LOGW(TAG, "HTTP响应数据过大，截断处理");
                return ESP_FAIL;
            }
            
            // 复制数据到缓冲区
            memcpy(response->data + response->size, evt->data, evt->data_len);
            response->size += evt->data_len;
            response->data[response->size] = '\0';
            break;
            
        case HTTP_EVENT_ON_FINISH:
            ESP_LOGI(TAG, "HTTP请求完成，接收数据: %zu字节", response->size);
            break;
            
        case HTTP_EVENT_ERROR:
            ESP_LOGE(TAG, "HTTP请求错误");
            break;
            
        default:
            break;
    }
    return ESP_OK;
}
```

### 3.2 通用HTTP请求函数

```c
/**
 * @brief 执行HTTP请求
 */
static weather_error_t perform_http_request(const char *url, http_response_data_t *response) {
    ESP_LOGI(TAG, "发送HTTP请求: %s", url);

    // 安全检查：检查可用内存
    size_t free_heap = esp_get_free_heap_size();
    ESP_LOGI(TAG, "当前可用内存: %d 字节", free_heap);

    // 检测异常内存值
    if (free_heap > 10 * 1024 * 1024 || free_heap < 1024) {
        ESP_LOGE(TAG, "检测到异常内存值: %d，系统可能不稳定，跳过请求", free_heap);
        return WEATHER_ERR_NETWORK;
    }

    if (free_heap < WEATHER_MAX_RESPONSE_SIZE * 2) {
        ESP_LOGE(TAG, "可用内存不足，跳过天气请求");
        return WEATHER_ERR_NETWORK;
    }

    // 分配响应缓冲区
    response->data = malloc(WEATHER_MAX_RESPONSE_SIZE);
    if (response->data == NULL) {
        ESP_LOGE(TAG, "HTTP响应缓冲区分配失败");
        return WEATHER_ERR_NETWORK;
    }

    response->size = 0;
    response->capacity = WEATHER_MAX_RESPONSE_SIZE;
    response->data[0] = '\0';
    
    // 初始化HTTP客户端配置
    esp_http_client_config_t config = {
        .url = url,
        .method = HTTP_METHOD_GET,
        .timeout_ms = WEATHER_HTTP_TIMEOUT_MS,
        .event_handler = http_event_handler,
        .user_data = response,
        .transport_type = HTTP_TRANSPORT_OVER_TCP,
        .is_async = false,
        .buffer_size = 4096,
        .buffer_size_tx = 1024,
        .user_agent = "ESP32-Weather-Client/1.0",
        .disable_auto_redirect = false,
        .max_redirection_count = 3,
    };

    // 创建HTTP客户端
    esp_http_client_handle_t http_client = esp_http_client_init(&config);
    if (http_client == NULL) {
        ESP_LOGE(TAG, "HTTP客户端初始化失败");
        free(response->data);
        return WEATHER_ERR_NETWORK;
    }

    // 执行HTTP请求
    esp_err_t err = esp_http_client_perform(http_client);
    int status_code = esp_http_client_get_status_code(http_client);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "HTTP请求失败: %s", esp_err_to_name(err));
        esp_http_client_cleanup(http_client);
        free(response->data);
        return WEATHER_ERR_NETWORK;
    }

    if (status_code != 200) {
        ESP_LOGE(TAG, "HTTP状态码错误: %d", status_code);
        esp_http_client_cleanup(http_client);
        free(response->data);
        return WEATHER_ERR_NETWORK;
    }

    ESP_LOGI(TAG, "HTTP请求成功，状态码: %d，数据大小: %zu字节", status_code, response->size);

    esp_http_client_cleanup(http_client);
    return WEATHER_OK;
}
```

---

## 📍 4. IP定位服务

### 4.1 IP定位实现

```c
/**
 * @brief 通过IP获取当前位置信息
 */
weather_error_t amap_get_location_by_ip(amap_weather_client_handle_t client, 
                                       location_info_t *location) {
    if (client == NULL || location == NULL) {
        return WEATHER_ERR_INVALID_PARAM;
    }
    
    // 构建请求URL
    char url[512];
    snprintf(url, sizeof(url), "%s?key=%s&output=json", 
             AMAP_IP_LOCATION_URL, client->api_key);
    
    // 发送HTTP请求
    http_response_data_t response;
    weather_error_t result = perform_http_request(url, &response);
    if (result != WEATHER_OK) {
        return result;
    }
    
    // 解析JSON响应
    cJSON *json = cJSON_Parse(response.data);
    free(response.data);
    
    if (json == NULL) {
        ESP_LOGE(TAG, "JSON解析失败");
        return WEATHER_ERR_PARSE_FAILED;
    }
    
    // 检查状态码
    cJSON *status = cJSON_GetObjectItem(json, "status");
    if (status == NULL || !cJSON_IsString(status) || strcmp(status->valuestring, "1") != 0) {
        ESP_LOGE(TAG, "IP定位API返回错误状态");
        cJSON_Delete(json);
        return WEATHER_ERR_API_LIMIT;
    }
    
    // 解析位置信息
    cJSON *province = cJSON_GetObjectItem(json, "province");
    cJSON *city = cJSON_GetObjectItem(json, "city");
    cJSON *district = cJSON_GetObjectItem(json, "district");
    cJSON *adcode = cJSON_GetObjectItem(json, "adcode");
    cJSON *rectangle = cJSON_GetObjectItem(json, "rectangle");
    
    // 填充位置信息结构体
    if (province && cJSON_IsString(province)) {
        strncpy(location->province, province->valuestring, sizeof(location->province) - 1);
    }
    if (city && cJSON_IsString(city)) {
        strncpy(location->city, city->valuestring, sizeof(location->city) - 1);
    }
    if (district && cJSON_IsString(district)) {
        strncpy(location->district, district->valuestring, sizeof(location->district) - 1);
    }
    if (adcode && cJSON_IsString(adcode)) {
        strncpy(location->adcode, adcode->valuestring, sizeof(location->adcode) - 1);
    }
    if (rectangle && cJSON_IsString(rectangle)) {
        strncpy(location->rectangle, rectangle->valuestring, sizeof(location->rectangle) - 1);
    }
    
    cJSON_Delete(json);
    
    ESP_LOGI(TAG, "IP定位成功: %s %s %s (adcode: %s)", 
             location->province, location->city, location->district, location->adcode);
    
    return WEATHER_OK;
}
```

---

## 🌤️ 5. 天气信息获取

### 5.1 天气数据查询

```c
/**
 * @brief 获取指定城市的天气信息
 */
weather_error_t amap_get_weather_info(amap_weather_client_handle_t client,
                                     const char *city_adcode,
                                     weather_info_t *weather) {
    if (client == NULL || city_adcode == NULL || weather == NULL) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    // 构建请求URL
    char url[512];
    snprintf(url, sizeof(url), "%s?key=%s&city=%s&output=json&extensions=base",
             AMAP_WEATHER_URL, client->api_key, city_adcode);

    // 发送HTTP请求
    http_response_data_t response;
    weather_error_t result = perform_http_request(url, &response);
    if (result != WEATHER_OK) {
        return result;
    }

    // 解析JSON响应
    cJSON *json = cJSON_Parse(response.data);
    free(response.data);

    if (json == NULL) {
        ESP_LOGE(TAG, "天气JSON解析失败");
        return WEATHER_ERR_PARSE_FAILED;
    }

    // 检查状态码
    cJSON *status = cJSON_GetObjectItem(json, "status");
    if (status == NULL || !cJSON_IsString(status) || strcmp(status->valuestring, "1") != 0) {
        ESP_LOGE(TAG, "天气API返回错误状态");
        cJSON_Delete(json);
        return WEATHER_ERR_API_LIMIT;
    }

    // 获取lives数组
    cJSON *lives = cJSON_GetObjectItem(json, "lives");
    if (lives == NULL || !cJSON_IsArray(lives) || cJSON_GetArraySize(lives) == 0) {
        ESP_LOGE(TAG, "天气数据格式错误");
        cJSON_Delete(json);
        return WEATHER_ERR_PARSE_FAILED;
    }

    // 获取第一个天气数据项
    cJSON *weather_item = cJSON_GetArrayItem(lives, 0);
    if (weather_item == NULL) {
        ESP_LOGE(TAG, "无法获取天气数据项");
        cJSON_Delete(json);
        return WEATHER_ERR_NO_DATA;
    }

    // 解析天气字段
    cJSON *province = cJSON_GetObjectItem(weather_item, "province");
    cJSON *city = cJSON_GetObjectItem(weather_item, "city");
    cJSON *adcode = cJSON_GetObjectItem(weather_item, "adcode");
    cJSON *weather_desc = cJSON_GetObjectItem(weather_item, "weather");
    cJSON *temperature = cJSON_GetObjectItem(weather_item, "temperature");
    cJSON *winddirection = cJSON_GetObjectItem(weather_item, "winddirection");
    cJSON *windpower = cJSON_GetObjectItem(weather_item, "windpower");
    cJSON *humidity = cJSON_GetObjectItem(weather_item, "humidity");
    cJSON *reporttime = cJSON_GetObjectItem(weather_item, "reporttime");

    // 填充天气信息结构体
    memset(weather, 0, sizeof(weather_info_t));
    
    if (province && cJSON_IsString(province)) {
        strncpy(weather->province, province->valuestring, sizeof(weather->province) - 1);
    }
    if (city && cJSON_IsString(city)) {
        strncpy(weather->city, city->valuestring, sizeof(weather->city) - 1);
    }
    if (adcode && cJSON_IsString(adcode)) {
        strncpy(weather->adcode, adcode->valuestring, sizeof(weather->adcode) - 1);
    }
    if (weather_desc && cJSON_IsString(weather_desc)) {
        strncpy(weather->weather, weather_desc->valuestring, sizeof(weather->weather) - 1);
    }
    if (temperature && cJSON_IsString(temperature)) {
        strncpy(weather->temperature, temperature->valuestring, sizeof(weather->temperature) - 1);
    }
    if (winddirection && cJSON_IsString(winddirection)) {
        strncpy(weather->winddirection, winddirection->valuestring, sizeof(weather->winddirection) - 1);
    }
    if (windpower && cJSON_IsString(windpower)) {
        strncpy(weather->windpower, windpower->valuestring, sizeof(weather->windpower) - 1);
    }
    if (humidity && cJSON_IsString(humidity)) {
        strncpy(weather->humidity, humidity->valuestring, sizeof(weather->humidity) - 1);
    }
    if (reporttime && cJSON_IsString(reporttime)) {
        strncpy(weather->reporttime, reporttime->valuestring, sizeof(weather->reporttime) - 1);
    }

    // 设置缓存时间戳
    weather->cache_time = esp_timer_get_time() / 1000;

    cJSON_Delete(json);

    ESP_LOGI(TAG, "天气信息获取成功: %s %s，%s，%s°C，%s %s级，湿度%s%%",
             weather->province, weather->city, weather->weather, weather->temperature,
             weather->winddirection, weather->windpower, weather->humidity);

    return WEATHER_OK;
}
```

### 5.2 组合定位和天气查询

```c
/**
 * @brief 获取当前位置的天气信息（组合接口）
 */
weather_error_t amap_get_current_weather(amap_weather_client_handle_t client,
                                        weather_info_t *weather) {
    if (client == NULL || weather == NULL) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "开始获取当前位置天气信息");

    // 检查是否有手动设置的区县编码
    char target_adcode[16];
    weather_error_t result = amap_get_manual_district(target_adcode, sizeof(target_adcode));
    if (result != WEATHER_OK) {
        // 没有手动设置，使用默认区县编码
        strncpy(target_adcode, DEFAULT_DISTRICT_ADCODE, sizeof(target_adcode) - 1);
        target_adcode[sizeof(target_adcode) - 1] = '\0';
        ESP_LOGI(TAG, "使用默认区县编码: %s", target_adcode);
    } else {
        ESP_LOGI(TAG, "使用手动设置的区县编码: %s", target_adcode);
    }

    // 如果使用的是默认区县编码，先尝试IP定位获取更准确的位置
    if (strcmp(target_adcode, DEFAULT_DISTRICT_ADCODE) == 0) {
        location_info_t location;
        result = amap_get_location_by_ip(client, &location);
        if (result == WEATHER_OK && strlen(location.adcode) > 0) {
            // 如果IP定位成功且返回了adcode，使用IP定位的结果
            strncpy(target_adcode, location.adcode, sizeof(target_adcode) - 1);
            target_adcode[sizeof(target_adcode) - 1] = '\0';
            ESP_LOGI(TAG, "IP定位成功，使用定位结果: %s", target_adcode);
        } else {
            ESP_LOGI(TAG, "IP定位失败，使用默认区县: %s", target_adcode);
        }
    }

    // 使用确定的adcode获取天气
    result = amap_get_weather_info(client, target_adcode, weather);
    if (result != WEATHER_OK) {
        ESP_LOGE(TAG, "获取天气信息失败");
        return result;
    }

    return WEATHER_OK;
}
```

---

## 💾 6. NVS缓存系统

### 6.1 缓存保存

```c
/**
 * @brief 保存天气信息到NVS缓存
 */
weather_error_t amap_save_weather_cache(const weather_info_t *weather) {
    if (weather == NULL) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(WEATHER_NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "打开NVS失败: %s", esp_err_to_name(err));
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 保存天气数据
    err = nvs_set_blob(nvs_handle, WEATHER_NVS_KEY_WEATHER, weather, sizeof(weather_info_t));
    if (err == ESP_OK) {
        // 保存时间戳
        uint64_t timestamp = esp_timer_get_time() / 1000;
        err = nvs_set_u64(nvs_handle, WEATHER_NVS_KEY_TIMESTAMP, timestamp);
    }

    if (err == ESP_OK) {
        err = nvs_commit(nvs_handle);
    }

    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "保存天气缓存失败: %s", esp_err_to_name(err));
        return WEATHER_ERR_CACHE_FAILED;
    }

    ESP_LOGI(TAG, "天气信息已缓存: %s %s，%s，%s°C", 
             weather->province, weather->city, weather->weather, weather->temperature);

    return WEATHER_OK;
}
```

### 6.2 缓存加载

```c
/**
 * @brief 从NVS缓存加载天气信息
 */
weather_error_t amap_load_weather_cache(weather_info_t *weather) {
    if (weather == NULL) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(WEATHER_NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 检查缓存时间戳
    uint64_t cached_timestamp = 0;
    err = nvs_get_u64(nvs_handle, WEATHER_NVS_KEY_TIMESTAMP, &cached_timestamp);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 检查缓存是否过期
    uint64_t current_time = esp_timer_get_time() / 1000;
    if (current_time - cached_timestamp > WEATHER_CACHE_DURATION_MS) {
        ESP_LOGI(TAG, "天气缓存已过期");
        nvs_close(nvs_handle);
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 加载天气数据
    size_t required_size = sizeof(weather_info_t);
    err = nvs_get_blob(nvs_handle, WEATHER_NVS_KEY_WEATHER, weather, &required_size);
    
    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "加载天气缓存失败: %s", esp_err_to_name(err));
        return WEATHER_ERR_CACHE_FAILED;
    }

    ESP_LOGI(TAG, "从缓存加载天气信息: %s %s，%s，%s°C", 
             weather->province, weather->city, weather->weather, weather->temperature);

    return WEATHER_OK;
}
```

---

## 🎙️ 7. 智能播报生成

### 7.1 天气播报文本格式化

```c
/**
 * @brief 格式化天气信息为语音播报文本
 */
weather_error_t amap_format_weather_text(const weather_info_t *weather,
                                        char *text_buffer,
                                        size_t buffer_size) {
    if (weather == NULL || text_buffer == NULL || buffer_size == 0) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    // 获取当前时间和时间段描述
    time_t now;
    time(&now);
    struct tm timeinfo;
    localtime_r(&now, &timeinfo);

    // 生成时间问候语
    const char *time_greeting = get_time_greeting(&timeinfo);
    
    // 生成天气建议
    const char *weather_advice = generate_weather_advice(weather);

    // 格式化完整播报文本
    int ret = snprintf(text_buffer, buffer_size,
                       "今天是%04d年%02d月%02d日%s，%s市天气%s，温度%s度，%s%s",
                       timeinfo.tm_year + 1900,
                       timeinfo.tm_mon + 1,
                       timeinfo.tm_mday,
                       get_weekday_name(timeinfo.tm_wday),
                       weather->city,
                       weather->weather,
                       weather->temperature,
                       time_greeting,
                       weather_advice);

    if (ret >= buffer_size) {
        ESP_LOGW(TAG, "天气播报文本被截断");
        return WEATHER_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "天气播报文本生成完成: %s", text_buffer);
    return WEATHER_OK;
}
```

### 7.2 天气建议生成

```c
/**
 * @brief 根据天气状况生成建议
 */
static const char* generate_weather_advice(const weather_info_t *weather) {
    if (weather == NULL) {
        return "";
    }

    // 检查温度
    int temp = atoi(weather->temperature);

    // 检查天气现象并生成提示
    if (strstr(weather->weather, "雨") != NULL) {
        return "，有降雨，注意带雨具，行车减速慢行";
    } else if (strstr(weather->weather, "雪") != NULL) {
        return "，有降雪，路面湿滑，谨慎驾驶";
    } else if (strstr(weather->weather, "雾") != NULL || strstr(weather->weather, "霾") != NULL) {
        return "，有雾霾，能见度低，开启雾灯慢行";
    } else if (strstr(weather->weather, "风") != NULL) {
        // 检查风力
        int wind_level = 0;
        if (strstr(weather->windpower, "级") != NULL) {
            wind_level = atoi(weather->windpower);
        }
        if (wind_level >= 6) {
            return "，风力较大，注意行车安全";
        }
    } else if (temp >= 35) {
        return "，高温天气，注意防暑降温，检查车辆冷却系统";
    } else if (temp <= 0) {
        return "，气温较低，注意保暖，检查车辆防冻液";
    } else if (strstr(weather->weather, "晴") != NULL) {
        return "，天气晴朗，适合出行";
    }

    return "，请注意行车安全";
}
```

---

## 🔧 8. 手动区县设置

### 8.1 设置手动区县编码

```c
/**
 * @brief 设置手动区县编码（精确定位）
 */
weather_error_t amap_set_manual_district(const char *adcode) {
    if (adcode == NULL || strlen(adcode) == 0) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(WEATHER_NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "打开NVS失败: %s", esp_err_to_name(err));
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 保存手动区县编码
    err = nvs_set_str(nvs_handle, WEATHER_NVS_KEY_MANUAL_ADCODE, adcode);
    if (err == ESP_OK) {
        err = nvs_commit(nvs_handle);
    }

    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "保存手动区县编码失败: %s", esp_err_to_name(err));
        return WEATHER_ERR_CACHE_FAILED;
    }

    ESP_LOGI(TAG, "手动区县编码已设置: %s", adcode);
    return WEATHER_OK;
}
```

### 8.2 获取手动区县编码

```c
/**
 * @brief 获取手动设置的区县编码
 */
weather_error_t amap_get_manual_district(char *adcode, size_t adcode_size) {
    if (adcode == NULL || adcode_size == 0) {
        return WEATHER_ERR_INVALID_PARAM;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(WEATHER_NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return WEATHER_ERR_CACHE_FAILED;
    }

    // 获取手动区县编码
    err = nvs_get_str(nvs_handle, WEATHER_NVS_KEY_MANUAL_ADCODE, adcode, &adcode_size);
    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        return WEATHER_ERR_CACHE_FAILED;
    }

    ESP_LOGI(TAG, "获取手动区县编码: %s", adcode);
    return WEATHER_OK;
}
```

---

---

## 📋 9. 完整的初始化流程

### 9.1 系统初始化

```c
/**
 * @brief 初始化高德天气系统
 */
esp_err_t amap_weather_system_init(void) {
    esp_err_t ret = ESP_OK;

    ESP_LOGI("WEATHER_SYSTEM", "开始初始化高德天气系统...");

    // 步骤1：初始化NVS
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 步骤2：创建天气客户端
    amap_weather_client_handle_t client = amap_weather_client_create();
    if (client == NULL) {
        ESP_LOGE("WEATHER_SYSTEM", "高德天气客户端创建失败");
        return ESP_FAIL;
    }

    // 步骤3：测试网络连通性
    weather_error_t weather_err = amap_test_connectivity(client);
    if (weather_err != WEATHER_OK) {
        ESP_LOGW("WEATHER_SYSTEM", "网络连通性测试失败，将在网络可用时重试");
    }

    // 步骤4：初始化天气播报器
    weather_announcer_handle_t announcer = weather_announcer_create();
    if (announcer == NULL) {
        ESP_LOGE("WEATHER_SYSTEM", "天气播报器创建失败");
        amap_weather_client_destroy(client);
        return ESP_FAIL;
    }

    ESP_LOGI("WEATHER_SYSTEM", "🎉 高德天气系统初始化完成");
    return ESP_OK;
}
```

### 9.2 天气播报器集成

```c
/**
 * @brief 天气播报器配置
 */
typedef struct {
    bool auto_announce_enabled;     // 自动播报开关
    bool startup_announce_enabled;  // 启动播报开关
    uint32_t update_interval_ms;    // 更新间隔
    uint32_t cache_duration_ms;     // 缓存时长
} weather_config_t;

/**
 * @brief 创建天气播报器
 */
weather_announcer_handle_t weather_announcer_create(void) {
    weather_announcer_t *announcer = malloc(sizeof(weather_announcer_t));
    if (announcer == NULL) {
        ESP_LOGE(TAG, "天气播报器内存分配失败");
        return NULL;
    }

    // 初始化配置
    announcer->config.auto_announce_enabled = true;
    announcer->config.startup_announce_enabled = true;
    announcer->config.update_interval_ms = 3600000; // 1小时
    announcer->config.cache_duration_ms = 1800000;  // 30分钟

    // 创建天气客户端
    announcer->weather_client = amap_weather_client_create();
    if (announcer->weather_client == NULL) {
        free(announcer);
        return NULL;
    }

    announcer->initialized = true;
    ESP_LOGI(TAG, "天气播报器创建成功");

    return announcer;
}
```

---

## 🎯 10. API参考手册

### 10.1 主要API函数

#### 10.1.1 客户端管理
```c
// 创建天气客户端
amap_weather_client_handle_t amap_weather_client_create(void);

// 销毁天气客户端
void amap_weather_client_destroy(amap_weather_client_handle_t client);

// 测试网络连通性
weather_error_t amap_test_connectivity(amap_weather_client_handle_t client);
```

#### 10.1.2 定位服务
```c
// 通过IP获取位置信息
weather_error_t amap_get_location_by_ip(amap_weather_client_handle_t client,
                                       location_info_t *location);

// 设置手动区县编码
weather_error_t amap_set_manual_district(const char *adcode);

// 清除手动区县编码
weather_error_t amap_clear_manual_district(void);
```

#### 10.1.3 天气查询
```c
// 获取指定城市天气
weather_error_t amap_get_weather_info(amap_weather_client_handle_t client,
                                     const char *city_adcode,
                                     weather_info_t *weather);

// 获取当前位置天气
weather_error_t amap_get_current_weather(amap_weather_client_handle_t client,
                                        weather_info_t *weather);
```

#### 10.1.4 缓存管理
```c
// 保存天气缓存
weather_error_t amap_save_weather_cache(const weather_info_t *weather);

// 加载天气缓存
weather_error_t amap_load_weather_cache(weather_info_t *weather);
```

#### 10.1.5 播报功能
```c
// 格式化播报文本
weather_error_t amap_format_weather_text(const weather_info_t *weather,
                                        char *text_buffer,
                                        size_t buffer_size);

// 手动触发天气播报
weather_error_t weather_announcer_manual_announce(weather_announcer_handle_t announcer);
```

---

## 🚀 11. 性能优化建议

### 11.1 网络优化

1. **请求频率控制**
   - 30分钟缓存机制减少API调用
   - 智能重试策略避免频繁失败
   - 网络状态检查优化请求时机

2. **数据传输优化**
   - 使用HTTP而非HTTPS减少SSL开销
   - 限制响应数据大小到1KB
   - 压缩JSON数据传输

3. **错误处理优化**
   - 指数退避重试算法
   - 网络异常时使用缓存数据
   - 超时机制防止长时间阻塞

### 11.2 内存优化

1. **内存分配策略**
   - 使用标准malloc避免内存管理器冲突
   - 及时释放HTTP响应缓冲区
   - 检查异常内存值防止系统不稳定

2. **缓存优化**
   - NVS存储持久化缓存
   - 定期清理过期缓存数据
   - 限制缓存数据大小

### 11.3 播报优化

1. **智能播报策略**
   - 根据天气状况生成个性化建议
   - 时间段问候语增强用户体验
   - 避免重复播报相同内容

2. **文本优化**
   - 简洁明了的播报内容
   - 符合中文语音习惯的表达
   - 重要信息优先播报

---

## 📚 12. 故障排除指南

### 12.1 常见问题

| 问题现象 | 可能原因 | 解决方案 |
|----------|----------|----------|
| API调用失败 | 密钥错误或过期 | 检查AMAP_API_KEY配置 |
| 网络请求超时 | 网络连接不稳定 | 增加超时时间，检查WiFi |
| JSON解析失败 | 响应格式变化 | 检查API版本和响应格式 |
| 定位失败 | IP定位服务异常 | 使用手动区县编码备选方案 |
| 缓存读取失败 | NVS存储异常 | 清除NVS分区重新初始化 |

### 12.2 调试方法

```c
// 启用详细日志
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG

// 网络状态监控
void weather_monitor_network_status(void) {
    // 检查WiFi连接状态
    wifi_ap_record_t ap_info;
    esp_err_t ret = esp_wifi_sta_get_ap_info(&ap_info);
    if (ret == ESP_OK) {
        ESP_LOGI("NETWORK", "WiFi连接正常 - RSSI: %d", ap_info.rssi);
    } else {
        ESP_LOGW("NETWORK", "WiFi连接异常: %s", esp_err_to_name(ret));
    }
}

// API调用统计
void weather_print_api_stats(amap_weather_client_handle_t client) {
    ESP_LOGI("API_STATS", "API调用统计 - 总次数:%d, 最后调用:%lld",
             client->request_count,
             client->last_request_time);
}

// 缓存状态检查
void weather_print_cache_status(void) {
    weather_info_t cached_weather;
    weather_error_t result = amap_load_weather_cache(&cached_weather);
    if (result == WEATHER_OK) {
        ESP_LOGI("CACHE", "缓存有效 - 城市:%s, 天气:%s, 温度:%s°C",
                 cached_weather.city, cached_weather.weather, cached_weather.temperature);
    } else {
        ESP_LOGI("CACHE", "缓存无效或过期");
    }
}
```

### 12.3 测试验证

```c
/**
 * @brief 天气系统功能测试
 */
esp_err_t weather_system_test(void) {
    ESP_LOGI("TEST", "开始天气系统功能测试...");

    // 测试1：创建客户端
    amap_weather_client_handle_t client = amap_weather_client_create();
    if (client == NULL) {
        ESP_LOGE("TEST", "❌ 客户端创建失败");
        return ESP_FAIL;
    }
    ESP_LOGI("TEST", "✅ 客户端创建成功");

    // 测试2：网络连通性
    weather_error_t result = amap_test_connectivity(client);
    if (result == WEATHER_OK) {
        ESP_LOGI("TEST", "✅ 网络连通性正常");
    } else {
        ESP_LOGW("TEST", "⚠️ 网络连通性异常");
    }

    // 测试3：IP定位
    location_info_t location;
    result = amap_get_location_by_ip(client, &location);
    if (result == WEATHER_OK) {
        ESP_LOGI("TEST", "✅ IP定位成功: %s %s %s",
                 location.province, location.city, location.district);
    } else {
        ESP_LOGW("TEST", "⚠️ IP定位失败");
    }

    // 测试4：天气查询
    weather_info_t weather;
    result = amap_get_current_weather(client, &weather);
    if (result == WEATHER_OK) {
        ESP_LOGI("TEST", "✅ 天气查询成功: %s，%s°C",
                 weather.weather, weather.temperature);
    } else {
        ESP_LOGE("TEST", "❌ 天气查询失败");
    }

    // 测试5：缓存功能
    if (result == WEATHER_OK) {
        result = amap_save_weather_cache(&weather);
        if (result == WEATHER_OK) {
            ESP_LOGI("TEST", "✅ 天气缓存保存成功");

            weather_info_t cached_weather;
            result = amap_load_weather_cache(&cached_weather);
            if (result == WEATHER_OK) {
                ESP_LOGI("TEST", "✅ 天气缓存加载成功");
            } else {
                ESP_LOGW("TEST", "⚠️ 天气缓存加载失败");
            }
        } else {
            ESP_LOGW("TEST", "⚠️ 天气缓存保存失败");
        }
    }

    // 测试6：播报文本生成
    if (result == WEATHER_OK) {
        char text_buffer[512];
        result = amap_format_weather_text(&weather, text_buffer, sizeof(text_buffer));
        if (result == WEATHER_OK) {
            ESP_LOGI("TEST", "✅ 播报文本生成成功: %s", text_buffer);
        } else {
            ESP_LOGW("TEST", "⚠️ 播报文本生成失败");
        }
    }

    // 清理资源
    amap_weather_client_destroy(client);

    ESP_LOGI("TEST", "🎉 天气系统功能测试完成");
    return ESP_OK;
}
```

---

## 🔧 13. 配置参数调整

### 13.1 区域配置

```c
// 全国主要城市adcode配置示例
#define BEIJING_ADCODE              "110000"  // 北京市
#define SHANGHAI_ADCODE             "310000"  // 上海市
#define GUANGZHOU_ADCODE            "440100"  // 广州市
#define SHENZHEN_ADCODE             "440300"  // 深圳市
#define HANGZHOU_ADCODE             "330100"  // 杭州市
#define NANJING_ADCODE              "320100"  // 南京市

// 根据项目需求选择默认城市
#ifndef DEFAULT_CITY_ADCODE
#define DEFAULT_CITY_ADCODE         SHENZHEN_ADCODE
#endif
```

### 13.2 性能参数调整

```c
// 根据网络环境调整的参数
#define WEATHER_HTTP_TIMEOUT_MS     15000   // 网络较差时增加到15秒
#define WEATHER_MAX_RETRY_COUNT     5       // 增加重试次数
#define WEATHER_RETRY_DELAY_MS      2000    // 重试间隔2秒

// 根据存储容量调整缓存
#define WEATHER_CACHE_DURATION_MS   3600000 // 增加到1小时缓存
#define WEATHER_MAX_CACHE_ENTRIES   10      // 最大缓存条目数
```

### 13.3 播报个性化配置

```c
// 播报内容个性化配置
typedef struct {
    bool include_humidity;          // 是否包含湿度信息
    bool include_wind;              // 是否包含风力信息
    bool include_advice;            // 是否包含天气建议
    bool use_formal_greeting;       // 是否使用正式问候语
    int max_text_length;            // 最大播报文本长度
} weather_announce_config_t;

// 默认播报配置
#define WEATHER_ANNOUNCE_DEFAULT_CONFIG() { \
    .include_humidity = false, \
    .include_wind = true, \
    .include_advice = true, \
    .use_formal_greeting = false, \
    .max_text_length = 200 \
}
```

---

**文档结束**

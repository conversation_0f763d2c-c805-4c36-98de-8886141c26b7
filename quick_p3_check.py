import struct

def quick_check(filename):
    with open(filename, 'rb') as f:
        # 读取前几个数据包
        data = f.read(1000)  # 读取前1000字节
    
    print(f"文件: {filename}")
    
    # 检查前3个数据包
    offset = 0
    for i in range(3):
        if offset + 4 > len(data):
            break
        
        # 解析头部
        payload_size = struct.unpack('>H', data[offset+2:offset+4])[0]
        print(f"数据包 {i+1} payload大小: {payload_size} 字节")
        
        offset += 4 + payload_size
        if offset > len(data):
            break
    
    return True

# 检查两个文件
print("=== 新文件分析 ===")
quick_check("J:/xiaozhi-esp32/audio_files/test_opus_001.p3")

print("\n=== 旧文件分析 ===")  
quick_check("J:/xiaozhi-esp32/spiffs_image/001.p3")

# 📝 ESP32小智文本交互功能模块

## 🎯 模块概述

本模块实现ESP32设备的文本交互功能，支持通过命令行发送文本给小智AI并接收音频回复。

## 🏗️ 架构设计

### 核心组件

1. **text_commands.h/cc** - 文本交互命令定义和实现
2. **text_interaction_manager.h/cc** - 文本交互管理器
3. **command_handlers.h/cc** - 各种快捷命令处理器

### 功能特性

- ✅ 无需唤醒直接发送文本
- ✅ 自动建立WebSocket连接
- ✅ 支持多种快捷命令
- ✅ 完全兼容现有语音功能
- ✅ 智能状态管理

## 📋 命令列表

### 基础命令
- `ask <message>` - 发送任意文本给小智
- `hello` - 快速问候

### 快捷命令
- `weather [city]` - 查询天气
- `time` - 询问时间
- `joke` - 请求讲笑话

## 🔧 集成方式

1. 在`network_console.cc`中注册命令
2. 在`application.cc`中集成文本交互管理器
3. 通过现有协议栈发送消息

## 📊 状态管理

- `text_interaction_mode_` - 标识当前是否为文本交互模式
- 自动状态切换：空闲 → 连接 → 监听 → 说话 → 空闲

## 🎵 音频处理

- 复用现有音频编解码器
- 自动播放服务器返回的TTS音频
- 支持Opus格式音频流

## 🔍 错误处理

- 连接失败自动重试
- 协议错误提示
- 状态异常恢复

## 📈 性能优化

- 最小化内存占用
- 复用现有组件
- 高效状态管理

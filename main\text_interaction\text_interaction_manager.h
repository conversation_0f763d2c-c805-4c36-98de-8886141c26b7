#pragma once

#include <string>
#include <functional>
#include <memory>

// 前向声明
class Application;
class Protocol;

namespace text_interaction {

/**
 * @brief 文本交互管理器
 * 
 * 负责管理ESP32设备的文本交互功能，包括：
 * - 无唤醒文本发送
 * - 自动连接管理
 * - 状态跟踪
 * - 错误处理
 */
class TextInteractionManager {
public:
    /**
     * @brief 构造函数
     * @param app 应用程序实例引用
     */
    explicit TextInteractionManager(Application& app);
    
    /**
     * @brief 析构函数
     */
    ~TextInteractionManager() = default;

    /**
     * @brief 初始化文本交互管理器
     * @return true 初始化成功，false 初始化失败
     */
    bool Initialize();

    /**
     * @brief 发送文本消息给小智
     * @param text 要发送的文本内容
     * @return true 发送成功，false 发送失败
     */
    bool SendTextToXiaozhi(const std::string& text);

    /**
     * @brief 检查是否处于文本交互模式
     * @return true 当前为文本交互模式，false 不是
     */
    bool IsInTextInteractionMode() const;

    /**
     * @brief 设置文本交互模式
     * @param enabled true 启用文本交互模式，false 禁用
     */
    void SetTextInteractionMode(bool enabled);

    /**
     * @brief 处理TTS停止事件
     * 当小智回复完成时调用此方法
     */
    void OnTtsStop();

    /**
     * @brief 获取管理器状态描述
     * @return 状态描述字符串
     */
    std::string GetStatusDescription() const;

private:
    /**
     * @brief 强制建立连接（如果需要）
     * @return true 连接成功或已连接，false 连接失败
     */
    bool ForceConnectIfNeeded();

    /**
     * @brief 检查协议是否可用
     * @return true 协议可用，false 协议不可用
     */
    bool IsProtocolReady() const;

    /**
     * @brief 记录发送状态
     * @param text 发送的文本
     * @param success 是否发送成功
     */
    void LogSendStatus(const std::string& text, bool success) const;

private:
    Application& app_;                    ///< 应用程序实例引用
    bool text_interaction_mode_;          ///< 文本交互模式标志
    bool initialized_;                    ///< 初始化状态标志
    
    // 统计信息
    size_t total_messages_sent_;          ///< 总发送消息数
    size_t successful_messages_;          ///< 成功发送消息数
    size_t failed_messages_;              ///< 失败发送消息数
};

} // namespace text_interaction

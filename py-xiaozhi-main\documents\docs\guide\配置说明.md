# 配置说明

## 配置系统概览

### 配置文件结构
py-xiaozhi 采用分层配置系统，支持多种配置管理方式：

```
config/
├── config.json          # 主配置文件（运行时配置）
└── efuse.json           # 设备身份文件（自动生成）
```

### 配置层次结构

1. **默认配置模板**
   - 位置：`src/utils/config_manager.py` 中的 `DEFAULT_CONFIG`
   - 作用：提供系统默认配置值
   - 用途：首次运行时自动生成配置文件的模板

2. **运行时配置文件**
   - 位置：`config/config.json`
   - 作用：存储用户自定义配置
   - 用途：系统运行时读取的实际配置

3. **设备身份文件**
   - 位置：`config/efuse.json`
   - 作用：存储设备唯一标识和激活状态
   - 用途：设备激活和身份验证

### 配置访问方式

配置系统支持点分隔路径访问，便于获取和修改嵌套配置：

```python
# 获取配置示例
from src.utils.config_manager import ConfigManager
config = ConfigManager.get_instance()

# 获取网络配置
websocket_url = config.get_config("SYSTEM_OPTIONS.NETWORK.WEBSOCKET_URL")

# 获取唤醒词配置
wake_words = config.get_config("WAKE_WORD_OPTIONS.WAKE_WORDS")

# 更新配置
config.update_config("WAKE_WORD_OPTIONS.USE_WAKE_WORD", True)
config.update_config("CAMERA.VLapi_key", "your_api_key_here")

# 重新加载配置
config.reload_config()
```

## 系统配置 (SYSTEM_OPTIONS)

### 基础系统配置

```json
{
  "SYSTEM_OPTIONS": {
    "CLIENT_ID": "自动生成的客户端ID",
    "DEVICE_ID": "设备MAC地址",
    "NETWORK": {
      "OTA_VERSION_URL": "https://api.tenclass.net/xiaozhi/ota/",
      "WEBSOCKET_URL": "wss://api.tenclass.net/xiaozhi/v1/",
      "WEBSOCKET_ACCESS_TOKEN": "访问令牌",
      "MQTT_INFO": {
        "endpoint": "mqtt.server.com",
        "client_id": "xiaozhi_client",
        "username": "your_username",
        "password": "your_password",
        "publish_topic": "xiaozhi/commands",
        "subscribe_topic": "xiaozhi/responses"
      },
      "ACTIVATION_VERSION": "v2",
      "AUTHORIZATION_URL": "https://xiaozhi.me/"
    }
  }
}
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `CLIENT_ID` | String | 自动生成 | 客户端唯一标识符 |
| `DEVICE_ID` | String | MAC地址 | 设备唯一标识符 |
| `OTA_VERSION_URL` | String | 官方OTA地址 | OTA配置获取地址 |
| `WEBSOCKET_URL` | String | 由OTA下发 | WebSocket服务器地址 |
| `WEBSOCKET_ACCESS_TOKEN` | String | 由OTA下发 | WebSocket访问令牌 |
| `ACTIVATION_VERSION` | String | "v2" | 激活协议版本 (v1/v2) |
| `AUTHORIZATION_URL` | String | "https://xiaozhi.me/" | 设备授权地址 |

## 服务端配置更换

### 更换自部署服务端

如需使用自行部署的服务端，只需修改OTA接口地址，系统会自动从OTA服务器获取WebSocket连接信息：

```json
{
  "SYSTEM_OPTIONS": {
    "NETWORK": {
      "OTA_VERSION_URL": "https://your-server.com/xiaozhi/ota/"
    }
  }
}
```

### 配置自动更新机制

系统启动时会通过以下流程自动更新配置：

1. **OTA配置获取**：向`OTA_VERSION_URL`发送POST请求
2. **配置自动更新**：系统自动更新MQTT和WebSocket配置
3. **连接建立**：使用更新后的配置建立连接

相关代码位置：
- OTA配置获取：`src/core/ota.py` 的 `fetch_and_update_config()` 方法
- 配置更新：`src/core/ota.py` 的 `update_websocket_config()` 和 `update_mqtt_config()` 方法

### 禁用配置自动更新

如果不需要配置自动更新，可以在以下位置注释相关代码：

**1. 禁用OTA配置获取**

在 `src/core/system_initializer.py` 中注释第三阶段：

```python
# async def stage_3_ota_config(self):
#     """
#     第三阶段：OTA获取配置.
#     """
#     # 注释掉整个方法内容
```

**2. 禁用WebSocket配置更新**

在 `src/core/ota.py` 中注释更新方法：

```python
async def update_websocket_config(self, response_data):
    """
    更新WebSocket配置信息.
    """
    # 注释掉配置更新逻辑
    return None
```

**3. 手动配置WebSocket连接**

直接在 `config/config.json` 中配置固定的连接信息：

```json
{
  "SYSTEM_OPTIONS": {
    "NETWORK": {
      "WEBSOCKET_URL": "wss://your-server.com/xiaozhi/v1/",
      "WEBSOCKET_ACCESS_TOKEN": "your_fixed_token"
    }
  }
}
```

## 唤醒词配置 (WAKE_WORD_OPTIONS)

### 语音唤醒设置

```json
{
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": false,
    "MODEL_PATH": "models/vosk-model-small-cn-0.22",
    "WAKE_WORDS": ["小智", "小美"],
    "SIMILARITY_THRESHOLD": 0.8
  }
}
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `USE_WAKE_WORD` | Boolean | false | 是否启用语音唤醒 |
| `MODEL_PATH` | String | "models/vosk-model-small-cn-0.22" | Vosk模型路径 |
| `WAKE_WORDS` | Array | ["小智", "小美"] | 唤醒词列表 |
| `SIMILARITY_THRESHOLD` | Float | 0.8 | 相似度阈值 |

### 唤醒词模型下载

```bash
# 下载中文小型模型（推荐）
wget -O models/vosk-model-small-cn-0.22.zip \
  https://alphacephei.com/vosk/models/vosk-model-small-cn-0.22.zip

# 解压到models目录
cd models && unzip vosk-model-small-cn-0.22.zip
```

## 摄像头配置 (CAMERA)

### 视觉识别设置

```json
{
  "CAMERA": {
    "camera_index": 0,
    "frame_width": 640,
    "frame_height": 480,
    "fps": 30,
    "Local_VL_url": "https://open.bigmodel.cn/api/paas/v4/",
    "VLapi_key": "your_zhipu_api_key",
    "models": "glm-4v-plus"
  }
}
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `camera_index` | Integer | 0 | 摄像头设备索引 |
| `frame_width` | Integer | 640 | 画面宽度 |
| `frame_height` | Integer | 480 | 画面高度 |
| `fps` | Integer | 30 | 帧率 |
| `Local_VL_url` | String | 智谱API地址 | 视觉模型API地址 |
| `VLapi_key` | String | "" | 智谱API密钥 |
| `models` | String | "glm-4v-plus" | 视觉模型名称 |

### 摄像头测试

```bash
# 测试摄像头功能
python scripts/camera_scanner.py

# 在程序中测试视觉识别
语音询问，看看摄像头前有什么？
```

## 快捷键配置 (SHORTCUTS)

### 全局快捷键设置

```json
{
  "SHORTCUTS": {
    "ENABLED": true,
    "MANUAL_PRESS": {
      "modifier": "ctrl",
      "key": "j",
      "description": "按住说话"
    },
    "AUTO_TOGGLE": {
      "modifier": "ctrl",
      "key": "k",
      "description": "自动对话"
    },
    "ABORT": {
      "modifier": "ctrl",
      "key": "q",
      "description": "中断对话"
    },
    "MODE_TOGGLE": {
      "modifier": "ctrl",
      "key": "m",
      "description": "切换模式"
    },
    "WINDOW_TOGGLE": {
      "modifier": "ctrl",
      "key": "w",
      "description": "显示/隐藏窗口"
    }
  }
}
```

### 快捷键说明

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+J` | 按住说话 | 按住期间录音，释放后发送 |
| `Ctrl+K` | 自动对话 | 开启/关闭自动对话模式 |
| `Ctrl+Q` | 中断对话 | 中断当前对话 |
| `Ctrl+M` | 切换模式 | 在不同对话模式间切换 |
| `Ctrl+W` | 显示/隐藏窗口 | 显示或隐藏主窗口 |

## 协议配置详解

### WebSocket 协议配置

WebSocket连接信息通常由OTA服务器自动下发，无需手动配置：

```json
{
  "SYSTEM_OPTIONS": {
    "NETWORK": {
      "WEBSOCKET_URL": "wss://your-server.com/xiaozhi/v1/",
      "WEBSOCKET_ACCESS_TOKEN": "your_access_token"
    }
  }
}
```

**配置要点：**
- URL必须以 `ws://` 或 `wss://` 开头
- 支持IP地址或域名
- 默认端口为8000，可根据服务器配置调整
- 访问令牌用于身份验证
- 通常由OTA服务器自动配置，无需手动设置

### MQTT 协议配置

```json
{
  "SYSTEM_OPTIONS": {
    "NETWORK": {
      "MQTT_INFO": {
        "endpoint": "mqtt.server.com",
        "port": 1883,
        "client_id": "xiaozhi_client_001",
        "username": "your_username",
        "password": "your_password",
        "publish_topic": "xiaozhi/commands",
        "subscribe_topic": "xiaozhi/responses",
        "qos": 1,
        "keep_alive": 60
      }
    }
  }
}
```

**配置要点：**
- `endpoint`: MQTT服务器地址
- `port`: 通常为1883（非加密）或8883（TLS加密）
- `client_id`: 客户端唯一标识
- `qos`: 消息质量等级（0-2）
- `keep_alive`: 心跳间隔（秒）

## 设备激活配置

### 激活版本说明

```json
{
  "SYSTEM_OPTIONS": {
    "NETWORK": {
      "ACTIVATION_VERSION": "v2",
      "AUTHORIZATION_URL": "https://xiaozhi.me/"
    }
  }
}
```

**版本差异：**
- **v1**: 简化激活流程，无需验证码
- **v2**: 完整激活流程，包含验证码验证

### 设备身份文件 (efuse.json)

```json
{
  "serial_number": "SN-E3E1F618-902e16dbe116",
  "hmac_key": "b5bf012dd518080532f928b70ed958799f34f9224e80dd4128795a70a5baca24",
  "activation_status": false,
  "mac_address": "00:11:22:33:44:55",
  "device_fingerprint": {
    "cpu_info": "...",
    "memory_info": "...",
    "disk_info": "..."
  }
}
```

**字段说明：**
- `serial_number`: 设备序列号
- `hmac_key`: 设备验证密钥
- `activation_status`: 激活状态
- `mac_address`: 设备MAC地址
- `device_fingerprint`: 设备指纹信息

## 配置管理实用技巧

### 1. 配置文件生成

```bash
# 首次运行自动生成配置
python main.py

# 重新生成默认配置
rm config/config.json
python main.py
```

### 2. 配置备份与恢复

```bash
# 备份配置
cp config/config.json config/config.json.bak

# 恢复配置
cp config/config.json.bak config/config.json
```

## 常见配置问题

### 1. WebSocket连接失败

**症状**: 无法连接到服务器

**解决方案**:
1. 检查OTA_VERSION_URL是否正确
2. 确保OTA服务器可以正常响应
3. 检查网络连接

### 2. 唤醒词不工作

**症状**: 语音唤醒无响应

**解决方案**:
```json
{
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": true,
    "MODEL_PATH": "models/vosk-model-small-cn-0.22",
    "WAKE_WORDS": ["小智", "小美"]
  }
}
```

**检查步骤**:
1. 确保模型文件存在
2. 检查音频设备权限
3. 测试麦克风功能

### 3. 摄像头无法使用

**症状**: 拍照功能异常

**解决方案**:
```json
{
  "CAMERA": {
    "camera_index": 0,
    "VLapi_key": "有效的智谱API密钥"
  }
}
```

**检查步骤**:
1. 运行摄像头测试脚本
2. 检查API密钥有效性
3. 验证网络连接

### 4. 设备激活失败

**症状**: 激活过程中出现错误

**解决方案**:
1. 检查网络连接
2. 确认激活版本设置
3. 清理设备身份文件：
   ```bash
   rm config/efuse.json
   python main.py  # 重新激活
   ```

## 配置文件模板

### 完整配置示例

```json
{
  "SYSTEM_OPTIONS": {
    "CLIENT_ID": "12345678-1234-1234-1234-123456789012",
    "DEVICE_ID": "00:11:22:33:44:55",
    "NETWORK": {
      "OTA_VERSION_URL": "https://api.tenclass.net/xiaozhi/ota/",
      "WEBSOCKET_URL": "wss://api.tenclass.net/xiaozhi/v1/",
      "WEBSOCKET_ACCESS_TOKEN": "your_access_token",
      "MQTT_INFO": {
        "endpoint": "mqtt.server.com",
        "client_id": "xiaozhi_client",
        "username": "your_username",
        "password": "your_password",
        "publish_topic": "xiaozhi/commands",
        "subscribe_topic": "xiaozhi/responses"
      },
      "ACTIVATION_VERSION": "v2",
      "AUTHORIZATION_URL": "https://xiaozhi.me/"
    }
  },
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": true,
    "MODEL_PATH": "models/vosk-model-small-cn-0.22",
    "WAKE_WORDS": ["小智", "小美", "你好小智"]
  },
  "CAMERA": {
    "camera_index": 0,
    "frame_width": 640,
    "frame_height": 480,
    "fps": 30,
    "Local_VL_url": "https://open.bigmodel.cn/api/paas/v4/",
    "VLapi_key": "your_zhipu_api_key",
    "models": "glm-4v-plus"
  },
  "SHORTCUTS": {
    "ENABLED": true,
    "MANUAL_PRESS": {
      "modifier": "ctrl",
      "key": "j",
      "description": "按住说话"
    },
    "AUTO_TOGGLE": {
      "modifier": "ctrl",
      "key": "k",
      "description": "自动对话"
    },
    "ABORT": {
      "modifier": "ctrl",
      "key": "q",
      "description": "中断对话"
    },
    "MODE_TOGGLE": {
      "modifier": "ctrl",
      "key": "m",
      "description": "切换模式"
    },
    "WINDOW_TOGGLE": {
      "modifier": "ctrl",
      "key": "w",
      "description": "显示/隐藏窗口"
    }
  }
}
```

### 语音唤醒设置

```json
{
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": false,
    "MODEL_PATH": "models/vosk-model-small-cn-0.22",
    "WAKE_WORDS": ["小智", "小美"],
    "SIMILARITY_THRESHOLD": 0.8
  }
}
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `USE_WAKE_WORD` | Boolean | false | 是否启用语音唤醒 |
| `MODEL_PATH` | String | "models/vosk-model-small-cn-0.22" | Vosk模型路径 |
| `WAKE_WORDS` | Array | ["小智", "小美"] | 唤醒词列表 |
| `SIMILARITY_THRESHOLD` | Float | 0.8 | 相似度阈值 |

### 唤醒词模型下载

```bash
# 下载中文小型模型（推荐）
wget -O models/vosk-model-small-cn-0.22.zip \
  https://alphacephei.com/vosk/models/vosk-model-small-cn-0.22.zip

# 解压到models目录
cd models && unzip vosk-model-small-cn-0.22.zip
```
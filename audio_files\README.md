# 本地音频文件目录

## 📁 目录说明
这个目录用于存放要转换为P3格式的音频文件。

## 🎵 支持的输入格式
- MP3
- WAV
- FLAC
- OGG

## 📝 文件命名规则
音频文件应该按照以下格式命名：
- **格式**: `XXX.扩展名`
- **编号**: 3位数字（001, 002, 003, ...）

### ✅ 正确示例
```
001.mp3  ✓
002.wav  ✓
003.flac ✓
010.mp3  ✓
099.ogg  ✓
```

### ❌ 错误示例
```
1.mp3     ❌ (不是3位数)
01.wav    ❌ (不是3位数)
hello.mp3 ❌ (不是数字)
```

## 🔄 转换流程

### 1. 准备音频文件
将您的音频文件放入此目录，确保文件名符合命名规则。

### 2. 转换为P3格式
使用转换脚本将音频文件转换为P3格式：

```bash
# 转换单个文件
python scripts/p3_tools/convert_audio_to_p3.py audio_files/001.mp3 spiffs_image/001.p3

# 批量转换（如果有批量脚本）
python scripts/p3_tools/batch_convert.py
```

### 3. 重新编译和烧录
转换完成后，重新编译固件：

```bash
# 使用自动编译脚本
J:\xiaozhi-esp32\xiaozhi_real_auto.bat
```

## 🎮 使用方法

### 查看可用音频文件
```bash
xiaozhi> local
```

### 播放指定音频文件
```bash
xiaozhi> local 001    # 播放 001.p3
xiaozhi> local 002    # 播放 002.p3
xiaozhi> local 010    # 播放 010.p3
```

## 📊 音频建议

### 文件大小
- **建议**: 每个文件 < 500KB
- **原因**: ESP32存储空间有限，P3格式已经高度压缩

### 音频质量
- **采样率**: 16kHz（推荐）
- **声道**: 单声道
- **时长**: 建议每个文件 < 30秒

### 内容建议
- 系统提示音
- 语音播报
- 简短的音乐片段
- 警告音效

## 🔧 故障排除

### 转换失败
1. 检查输入文件是否损坏
2. 确认文件格式是否支持
3. 检查文件名是否符合规则

### 播放失败
1. 确认文件已正确转换为P3格式
2. 检查文件是否在/audio目录中
3. 确认文件名格式正确（3位数字）

### 文件不显示
1. 重新编译和烧录固件
2. 检查SPIFFS分区是否正确构建
3. 确认文件在spiffs_image目录中

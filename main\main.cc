#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_event.h>
#include <esp_vfs.h>
#include <esp_spiffs.h>

#include "application.h"
#include "system_info.h"
#include "settings.h"
#include "network_console.h"

#define TAG "main"

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Mount audio partition
    ESP_LOGI(TAG, "Mounting audio partition...");
    esp_vfs_spiffs_conf_t audio_conf = {
        .base_path = "/audio",
        .partition_label = "audio",
        .max_files = 50,  // 增加到50个文件，适应3MB分区
        .format_if_mount_failed = false,
    };
    ret = esp_vfs_spiffs_register(&audio_conf);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to mount audio partition: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "Audio partition mounted successfully at /audio");
    }

    // 强制设置网络类型为4G (ML307)
    // 取消下面的注释来强制切换到4G
    /*
    {
        Settings settings("network", true);
        settings.SetInt("type", 1); // 1 = ML307 (4G), 0 = WiFi
        ESP_LOGI(TAG, "Network type set to 4G (ML307)");
    }
    */

    // Initialize network console for command line control
    NetworkConsole::GetInstance().Initialize();

    // Launch the application
    Application::GetInstance().Start();
}

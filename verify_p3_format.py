#!/usr/bin/env python3
import struct
import os

def verify_p3_format(filename):
    """深度验证P3文件格式是否正确"""
    
    print(f"🔍 深度验证P3文件: {filename}")
    print("=" * 60)
    
    if not os.path.exists(filename):
        print("❌ 文件不存在")
        return False
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    file_size = len(data)
    print(f"📁 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    # 验证文件大小是否合理（Opus编码应该比较小）
    if file_size > 150000:  # 150KB
        print("⚠️  警告: 文件过大，可能不是Opus编码")
    elif file_size < 10000:  # 10KB
        print("⚠️  警告: 文件过小，可能数据不完整")
    else:
        print("✅ 文件大小合理")
    
    # 分析P3数据包结构
    print(f"\n📦 分析P3数据包结构:")
    print("-" * 40)
    
    offset = 0
    packet_count = 0
    total_payload = 0
    payload_sizes = []
    invalid_packets = 0
    
    while offset + 4 <= file_size:
        # 解析BinaryProtocol3头部
        type_byte = data[offset]
        reserved = data[offset + 1]
        payload_size = struct.unpack('>H', data[offset+2:offset+4])[0]
        
        # 验证头部格式
        if type_byte != 0:
            print(f"⚠️  数据包 {packet_count+1}: type字节应该为0，实际为{type_byte}")
            invalid_packets += 1
        
        if reserved != 0:
            print(f"⚠️  数据包 {packet_count+1}: reserved字节应该为0，实际为{reserved}")
            invalid_packets += 1
        
        # 验证payload大小是否合理
        if payload_size == 0:
            print(f"❌ 数据包 {packet_count+1}: payload大小为0")
            invalid_packets += 1
        elif payload_size > 1000:
            print(f"⚠️  数据包 {packet_count+1}: payload过大({payload_size}字节)，可能是PCM数据")
            invalid_packets += 1
        elif payload_size < 20:
            print(f"⚠️  数据包 {packet_count+1}: payload过小({payload_size}字节)")
            invalid_packets += 1
        
        # 显示前5个数据包的详细信息
        if packet_count < 5:
            print(f"数据包 {packet_count+1}: type={type_byte}, reserved={reserved}, payload={payload_size}字节")
        
        payload_sizes.append(payload_size)
        total_payload += payload_size
        packet_count += 1
        
        offset += 4 + payload_size
        
        # 检查是否超出文件范围
        if offset > file_size:
            print(f"❌ 数据包 {packet_count} 超出文件范围")
            break
    
    # 统计分析
    print(f"\n📊 统计分析:")
    print("-" * 40)
    print(f"总数据包数: {packet_count}")
    print(f"无效数据包: {invalid_packets}")
    
    if payload_sizes:
        avg_payload = total_payload / packet_count
        min_payload = min(payload_sizes)
        max_payload = max(payload_sizes)
        
        print(f"平均payload大小: {avg_payload:.1f} 字节")
        print(f"payload大小范围: {min_payload} - {max_payload} 字节")
        
        # 估算音频时长
        frame_duration = 0.06  # 60ms
        estimated_duration = packet_count * frame_duration
        print(f"估算音频时长: {estimated_duration:.1f} 秒")
        
        # 判断格式
        print(f"\n🎯 格式判断:")
        print("-" * 40)
        
        if 50 <= avg_payload <= 300:
            print("✅ 判断: 这是正确的Opus编码P3文件")
            format_correct = True
        elif avg_payload > 1000:
            print("❌ 判断: 这很可能是PCM数据的P3文件 (错误格式)")
            format_correct = False
        else:
            print("⚠️  判断: 格式不确定，需要进一步验证")
            format_correct = None
        
        # 最终验证结果
        print(f"\n🏁 最终验证结果:")
        print("=" * 40)
        
        if invalid_packets == 0 and format_correct == True:
            print("🎉 ✅ 文件格式完全正确，可以安全使用！")
            return True
        elif invalid_packets == 0 and format_correct is None:
            print("⚠️  文件格式基本正确，但需要实际测试验证")
            return True
        else:
            print("❌ 文件格式存在问题，不建议使用")
            return False
    else:
        print("❌ 无法解析任何数据包")
        return False

if __name__ == "__main__":
    result = verify_p3_format("spiffs_image/001.p3")
    print(f"\n验证结果: {'通过' if result else '失败'}")

#!/usr/bin/env python3
"""
音频SPIFFS分区构建脚本
用于将音频文件打包到SPIFFS镜像中并烧录到ESP32
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目路径配置
PROJECT_ROOT = Path(__file__).parent.parent
AUDIO_SOURCE_DIR = PROJECT_ROOT / "audio_files"
SPIFFS_IMAGE_DIR = PROJECT_ROOT / "build" / "audio_spiffs"
SPIFFS_IMAGE_FILE = PROJECT_ROOT / "build" / "audio.bin"

# SPIFFS配置（与分区表一致）
SPIFFS_SIZE = 0x300000  # 3MB (与分区表中的audio分区大小一致)
SPIFFS_PAGE_SIZE = 256
SPIFFS_BLOCK_SIZE = 4096

def create_directories():
    """创建必要的目录"""
    AUDIO_SOURCE_DIR.mkdir(exist_ok=True)
    SPIFFS_IMAGE_DIR.mkdir(parents=True, exist_ok=True)
    print(f"✓ 创建目录: {AUDIO_SOURCE_DIR}")
    print(f"✓ 创建目录: {SPIFFS_IMAGE_DIR}")

def copy_audio_files():
    """复制音频文件到SPIFFS镜像目录"""
    if not AUDIO_SOURCE_DIR.exists():
        print(f"❌ 音频源目录不存在: {AUDIO_SOURCE_DIR}")
        print(f"请创建目录并放入音频文件: {AUDIO_SOURCE_DIR}")
        return False
    
    # 清空目标目录
    if SPIFFS_IMAGE_DIR.exists():
        shutil.rmtree(SPIFFS_IMAGE_DIR)
    SPIFFS_IMAGE_DIR.mkdir(parents=True)
    
    # 复制音频文件
    audio_files = []
    for ext in ['*.mp3', '*.wav', '*.p3']:
        audio_files.extend(AUDIO_SOURCE_DIR.glob(ext))
    
    if not audio_files:
        print(f"❌ 在 {AUDIO_SOURCE_DIR} 中未找到音频文件")
        print("支持的格式: .mp3, .wav, .p3")
        return False
    
    copied_count = 0
    for audio_file in audio_files:
        # 检查文件名格式（3位数字）
        name_without_ext = audio_file.stem
        if len(name_without_ext) == 3 and name_without_ext.isdigit():
            dest_file = SPIFFS_IMAGE_DIR / audio_file.name
            shutil.copy2(audio_file, dest_file)
            print(f"✓ 复制音频文件: {audio_file.name}")
            copied_count += 1
        else:
            print(f"⚠️  跳过文件 {audio_file.name} (文件名格式不正确，应为3位数字)")
    
    print(f"✓ 总共复制了 {copied_count} 个音频文件")
    return copied_count > 0

def build_spiffs_image():
    """构建SPIFFS镜像"""
    print("🔨 构建SPIFFS镜像...")
    
    # 查找mkspiffs工具
    mkspiffs_path = find_mkspiffs()
    if not mkspiffs_path:
        print("❌ 未找到mkspiffs工具")
        return False
    
    # 构建SPIFFS镜像
    cmd = [
        str(mkspiffs_path),
        "-c", str(SPIFFS_IMAGE_DIR),
        "-b", str(SPIFFS_BLOCK_SIZE),
        "-p", str(SPIFFS_PAGE_SIZE),
        "-s", str(SPIFFS_SIZE),
        str(SPIFFS_IMAGE_FILE)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ SPIFFS镜像构建成功: {SPIFFS_IMAGE_FILE}")
            return True
        else:
            print(f"❌ SPIFFS镜像构建失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 执行mkspiffs失败: {e}")
        return False

def find_mkspiffs():
    """查找mkspiffs工具"""
    # 常见的mkspiffs路径
    possible_paths = [
        # ESP-IDF工具链路径
        Path.home() / ".espressif" / "tools" / "mkspiffs",
        # 系统PATH中
        shutil.which("mkspiffs"),
        # 项目工具目录
        PROJECT_ROOT / "tools" / "mkspiffs"
    ]
    
    for path in possible_paths:
        if path and Path(path).exists():
            return path
        # 在目录中查找
        if path and Path(path).is_dir():
            for mkspiffs_file in Path(path).rglob("mkspiffs*"):
                if mkspiffs_file.is_file() and os.access(mkspiffs_file, os.X_OK):
                    return mkspiffs_file
    
    return None

def flash_spiffs_image():
    """烧录SPIFFS镜像到ESP32"""
    print("🔥 烧录SPIFFS镜像到ESP32...")
    
    if not SPIFFS_IMAGE_FILE.exists():
        print(f"❌ SPIFFS镜像文件不存在: {SPIFFS_IMAGE_FILE}")
        return False
    
    # 音频分区偏移地址（与分区表一致）
    AUDIO_PARTITION_OFFSET = "0x100000"
    
    cmd = [
        "esptool.py",
        "--chip", "esp32s3",
        "--port", "COM3",  # 根据实际端口修改
        "--baud", "921600",
        "write_flash",
        AUDIO_PARTITION_OFFSET,
        str(SPIFFS_IMAGE_FILE)
    ]
    
    try:
        result = subprocess.run(cmd)
        if result.returncode == 0:
            print("✓ SPIFFS镜像烧录成功")
            return True
        else:
            print("❌ SPIFFS镜像烧录失败")
            return False
    except Exception as e:
        print(f"❌ 执行esptool失败: {e}")
        print("请确保已安装esptool: pip install esptool")
        return False

def main():
    """主函数"""
    print("🎵 音频SPIFFS分区构建工具")
    print("=" * 50)
    
    # 创建目录
    create_directories()
    
    # 复制音频文件
    if not copy_audio_files():
        print("\n❌ 请在以下目录放入音频文件:")
        print(f"   {AUDIO_SOURCE_DIR}")
        print("文件命名格式: 001.mp3, 002.wav, 003.p3 等")
        return 1
    
    # 构建SPIFFS镜像
    if not build_spiffs_image():
        return 1
    
    # 询问是否烧录
    response = input("\n是否立即烧录到ESP32? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        if not flash_spiffs_image():
            return 1
    else:
        print(f"✓ SPIFFS镜像已生成: {SPIFFS_IMAGE_FILE}")
        print("您可以稍后使用以下命令烧录:")
        print(f"esptool.py --chip esp32s3 --port COM3 write_flash 0x100000 {SPIFFS_IMAGE_FILE}")
    
    print("\n🎉 完成!")
    return 0

if __name__ == "__main__":
    sys.exit(main())

#include "network_console.h"
#include "settings.h"
#include "board.h"
#include "boards/common/dual_network_board.h"
#include "application.h"

#include <esp_console.h>
#include <esp_log.h>
#include <esp_mac.h>
#include <esp_chip_info.h>
#include <esp_ota_ops.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <dirent.h>
#include <string.h>

#define TAG "NetworkConsole"

NetworkConsole& NetworkConsole::GetInstance() {
    static NetworkConsole instance;
    return instance;
}

void NetworkConsole::Initialize() {
    if (initialized_) {
        return;
    }

    esp_console_repl_t *repl = NULL;
    esp_console_repl_config_t repl_config = ESP_CONSOLE_REPL_CONFIG_DEFAULT();
    repl_config.max_cmdline_length = 1024;
    repl_config.prompt = "xiaozhi>";

    RegisterNetworkCommands();

    esp_console_dev_uart_config_t hw_config = ESP_CONSOLE_DEV_UART_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_uart(&hw_config, &repl_config, &repl));
    ESP_ERROR_CHECK(esp_console_start_repl(repl));

    initialized_ = true;
    ESP_LOGI(TAG, "Network console initialized. Type 'nethelp' for available commands.");
}

void NetworkConsole::RegisterNetworkCommands() {
    // 4G命令
    const esp_console_cmd_t cmd_4g = {
        .command = "4g",
        .help = "Switch to 4G network (ML307)",
        .hint = nullptr,
        .func = CmdSwitchTo4G,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_4g));

    // WiFi命令
    const esp_console_cmd_t cmd_wifi = {
        .command = "wifi",
        .help = "Switch to WiFi network",
        .hint = nullptr,
        .func = CmdSwitchToWiFi,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_wifi));

    // 网络状态命令
    const esp_console_cmd_t cmd_status = {
        .command = "status",
        .help = "Show current network status",
        .hint = nullptr,
        .func = CmdNetworkStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_status));

    // 帮助命令
    const esp_console_cmd_t cmd_help_net = {
        .command = "nethelp",
        .help = "Show network commands help",
        .hint = nullptr,
        .func = CmdHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_help_net));

    // 重启命令
    const esp_console_cmd_t cmd_reboot = {
        .command = "reboot",
        .help = "Reboot the device",
        .hint = nullptr,
        .func = CmdReboot,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_reboot));

    // 本地音频播放命令
    const esp_console_cmd_t cmd_local = {
        .command = "local",
        .help = "Play local audio files",
        .hint = "[file_number]",
        .func = CmdLocalAudio,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_local));
}

int NetworkConsole::CmdSwitchTo4G(int argc, char** argv) {
    printf("Switching to 4G network (ML307)...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    // 通过板卡类型字符串检查是否支持双网络
    std::string board_type = board.GetBoardType();
    if (board_type != "wifi" && board_type != "ml307") {
        printf("❌ Error: This device does not support dual network mode.\n");
        printf("   4G network is not available on this device.\n");
        printf("   Available commands: wifi, status, nethelp\n");
        return 1;
    }

    // 对于支持双网络的板卡，使用static_cast（我们已经通过类型检查确认了）
    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::ML307) {
        printf("✅ Already using 4G network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 1); // 1 = ML307 (4G)

    printf("Network type set to 4G. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

int NetworkConsole::CmdNetworkStatus(int argc, char** argv) {
    printf("\n=== Network Status ===\n");

    // 获取设备信息
    auto& board = Board::GetInstance();

    // 读取当前网络设置
    Settings settings("network", false);
    int network_type = settings.GetInt("type", 1); // 默认ML307

    // 通过板卡类型检查是否支持双网络
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        auto* dual_board = static_cast<DualNetworkBoard*>(&board);
        printf("Device Type: Dual Network (WiFi + 4G)\n");

        NetworkType current_type = dual_board->GetNetworkType();
        if (current_type == NetworkType::ML307) {
            printf("Current Network: 4G (ML307)\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        } else {
            printf("Current Network: WiFi\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        }
        printf("Supported Networks: WiFi, 4G (ML307)\n");
        printf("Network Switching: ✅ Available\n");
    } else {
        printf("Device Type: WiFi Only\n");
        printf("Current Network: WiFi (Only)\n");
        printf("Supported Networks: WiFi only\n");
        printf("Network Switching: ❌ Not available\n");
        if (network_type == 1) {
            printf("⚠️  Warning: 4G setting detected but device only supports WiFi\n");
        }
    }

    // 显示设备信息
    printf("\n=== Device Info ===\n");

    // MAC地址
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    printf("WiFi MAC: " MACSTR "\n", MAC2STR(mac));

    // 芯片信息
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    printf("Chip: %s Rev %d\n",
           chip_info.model == CHIP_ESP32 ? "ESP32" :
           chip_info.model == CHIP_ESP32S2 ? "ESP32-S2" :
           chip_info.model == CHIP_ESP32S3 ? "ESP32-S3" :
           chip_info.model == CHIP_ESP32C3 ? "ESP32-C3" : "Unknown",
           chip_info.revision);

    // 固件版本
    const esp_app_desc_t* app_desc = esp_ota_get_app_description();
    printf("Firmware: %s\n", app_desc->version);
    printf("Build Date: %s %s\n", app_desc->date, app_desc->time);

    printf("==================\n\n");

    return 0;
}

int NetworkConsole::CmdHelp(int argc, char** argv) {
    printf("\n=== Network Console Commands ===\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        printf("4g        - Switch to 4G network (ML307)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("local     - Play local audio files\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: Dual Network Board\n");
        printf("  Networks: WiFi + 4G (ML307)\n");
        printf("  Switching: ✅ Available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> 4g       # Switch to 4G\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
        printf("  xiaozhi> local    # List audio files\n");
        printf("  xiaozhi> local 001 # Play audio file 001\n");
    } else {
        printf("4g        - ❌ Not available (WiFi-only device)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("local     - Play local audio files\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: WiFi Only Board\n");
        printf("  Networks: WiFi only\n");
        printf("  Switching: ❌ Not available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
        printf("  xiaozhi> local    # List audio files\n");
        printf("  xiaozhi> local 001 # Play audio file 001\n");
    }
    printf("================================\n\n");

    return 0;
}

int NetworkConsole::CmdReboot(int argc, char** argv) {
    printf("Rebooting device in 3 seconds...\n");
    for (int i = 3; i > 0; i--) {
        printf("%d...\n", i);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    printf("Rebooting now!\n");
    esp_restart();
    return 0;
}

int NetworkConsole::CmdSwitchToWiFi(int argc, char** argv) {
    printf("Switching to WiFi network...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (!is_dual_network) {
        printf("✅ This device only supports WiFi network.\n");
        return 0;
    }

    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::WIFI) {
        printf("✅ Already using WiFi network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 0); // 0 = WiFi

    printf("Network type set to WiFi. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

int NetworkConsole::CmdLocalAudio(int argc, char** argv) {
    if (argc == 1) {
        // 显示可用的音频文件列表
        printf("\n=== Local Audio Files ===\n");

        DIR* dir = opendir("/spiffs");
        if (dir == nullptr) {
            printf("❌ Failed to open /spiffs directory\n");
            return 1;
        }

        struct dirent* entry;
        int file_count = 0;

        while ((entry = readdir(dir)) != nullptr) {
            // 检查是否是音频文件（.p3, .aaf格式）
            const char* name = entry->d_name;
            size_t len = strlen(name);

            if (len >= 7) { // 至少 "001.p3" 长度
                // 检查文件名格式：3位数字 + 扩展名
                if (isdigit(name[0]) && isdigit(name[1]) && isdigit(name[2]) && name[3] == '.') {
                    if (strstr(name, ".p3") || strstr(name, ".aaf")) {
                        printf("  %c%c%c - %s\n", name[0], name[1], name[2], name);
                        file_count++;
                    }
                }
            }
        }

        closedir(dir);

        if (file_count == 0) {
            printf("No audio files found in /spiffs\n");
            printf("\nTo add audio files:\n");
            printf("1. Convert MP3 to P3: python scripts/p3_tools/convert_audio_to_p3.py input.mp3 001.p3\n");
            printf("2. Place files in spiffs_image/ directory\n");
            printf("3. Rebuild and flash firmware\n");
        } else {
            printf("\nFound %d audio files\n", file_count);
            printf("\nUsage: local <number>  (e.g., local 001)\n");
        }

        printf("========================\n\n");
        return 0;
    }

    if (argc == 2) {
        // 播放指定的音频文件
        const char* file_number = argv[1];

        // 验证输入格式（应该是3位数字）
        if (strlen(file_number) != 3 || !isdigit(file_number[0]) ||
            !isdigit(file_number[1]) || !isdigit(file_number[2])) {
            printf("❌ Invalid file number. Use 3-digit format (e.g., 001, 002)\n");
            return 1;
        }

        // 尝试播放P3文件
        char p3_filename[32];
        snprintf(p3_filename, sizeof(p3_filename), "%s.p3", file_number);

        printf("🎵 Attempting to play: %s\n", p3_filename);

        // 使用Application的PlaySound方法播放音频
        try {
            Application::GetInstance().PlaySound(p3_filename);
            printf("✅ Playing: %s\n", p3_filename);
        } catch (...) {
            // 如果P3文件播放失败，尝试AAF文件
            char aaf_filename[32];
            snprintf(aaf_filename, sizeof(aaf_filename), "%s.aaf", file_number);

            printf("🎵 Trying AAF format: %s\n", aaf_filename);

            try {
                Application::GetInstance().PlaySound(aaf_filename);
                printf("✅ Playing: %s\n", aaf_filename);
            } catch (...) {
                printf("❌ Failed to play audio file %s (tried both .p3 and .aaf)\n", file_number);
                printf("Make sure the file exists in /spiffs directory\n");
                return 1;
            }
        }

        return 0;
    }

    printf("Usage:\n");
    printf("  local          - List available audio files\n");
    printf("  local <number> - Play audio file (e.g., local 001)\n");
    return 1;
}

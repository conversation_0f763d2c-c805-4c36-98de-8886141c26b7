name: Setup Inno Setup, Conda, and Build

on:
  push:
    branches:
      - main

jobs:
  setup-and-build:
    runs-on: windows-latest
    steps:
      # 检出代码
      - name: 检出代码
        uses: actions/checkout@v4

      # 安装 Inno Setup
      - name: 安装 Inno Setup（通过 Chocolatey）
        run: choco install innosetup -y
        shell: powershell

      # 下载 ChineseSimplified.isl 文件
      - name: 下载中文语言文件
        run: |
          $languageDir = "C:\Program Files (x86)\Inno Setup 6\Languages"
          
          # 确保目录存在
          if (-not (Test-Path $languageDir)) {
            New-Item -ItemType Directory -Path $languageDir -Force
          }
          
          # 下载中文语言文件
          Invoke-WebRequest -Uri "https://raw.githubusercontent.com/jrsoftware/issrc/main/Files/Languages/Unofficial/ChineseSimplified.isl" -OutFile "$languageDir\ChineseSimplified.isl"
          
          Write-Host "已下载中文语言文件到 $languageDir\ChineseSimplified.isl"
          
          # 验证文件是否存在
          if (Test-Path "$languageDir\ChineseSimplified.isl") {
            Write-Host "文件下载成功！"
          } else {
            Write-Error "文件下载失败！"
            exit 1
          }
        shell: powershell

      # 查找 ISCC.exe 路径并更新 build.json
      - name: 查找 ISCC.exe 路径并更新 build.json
        run: |
          # 获取 ISCC 路径
          $isccPath = Get-Command ISCC.exe | Select-Object -ExpandProperty Source
          
          # 设置环境变量
          $envLine = "ISCC_PATH=$($isccPath -replace '`r','' -replace '`n','')"
          [System.IO.File]::AppendAllText($env:GITHUB_ENV, "$envLine`n", [System.Text.Encoding]::UTF8)
          
          # 读取 JSON 内容
          $jsonPath = "build.json"
          $json = Get-Content $jsonPath -Raw | ConvertFrom-Json
          
          # 设置新的 Inno Setup 路径
          $json.inno_setup_path = $isccPath
          
          # 写回 JSON 文件，格式化为 UTF-8 编码
          $json | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonPath -Encoding UTF8
          
          Write-Host "已更新 build.json 中的 Inno Setup 路径为: $isccPath"
        shell: powershell

      # 安装 Miniconda 和 Conda
      - name: 安装 Miniconda 和 Conda
        uses: conda-incubator/setup-miniconda@v3
        with:
          python-version: "3.10"
          auto-update-conda: true
          auto-activate-base: false
          activate-environment: py-xiaozhi

      # 创建 Conda 环境并安装依赖
      - name: 创建 Conda 环境 py-xiaozhi
        shell: bash -el {0}
        run: conda create -n py-xiaozhi python=3.10 -y

      # 创建一个简单的 requirements.txt 文件（如果不存在）
      - name: 创建 requirements.txt
        run: |
          if (-not (Test-Path "requirements.txt")) {
            Set-Content -Path "requirements.txt" -Value ""
          }
        shell: powershell

      # 安装 Python 依赖（pip + conda + pyinstaller）
      - name: 安装 Python 依赖（pip + conda + pyinstaller）
        shell: bash -el {0}
        run: |
          conda activate py-xiaozhi
          
          # 安装依赖
          pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple
          conda install pyqt=5.15.10 opencv=4.10.0 -y
          pip install pyinstaller

      # 拉取 UnifyPy 仓库
      - name: 拉取 UnifyPy 仓库
        run: git clone https://github.com/huangjunsen0406/UnifyPy.git
        shell: bash

      # 运行 UnifyPy 构建项目
      - name: 运行 UnifyPy 构建项目
        shell: bash -el {0}
        run: |
          conda activate py-xiaozhi
          export PYTHONIOENCODING=utf-8
          python UnifyPy/main.py . --config build.json

      # 只上传 Inno Setup 安装程序
      - name: 上传安装程序
        uses: actions/upload-artifact@v4
        with:
          name: installer-setup
          path: installer/

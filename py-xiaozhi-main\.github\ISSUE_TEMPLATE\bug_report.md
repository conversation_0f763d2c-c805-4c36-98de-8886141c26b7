---
name: Bug 报告（Bug Report）
about: 反馈项目中的缺陷或问题
title: "[Bug] 简短描述问题"
labels: bug
assignees: ''
---

## 🐛 问题描述
<!-- 清晰简洁地描述问题是什么 -->

## 🔍 复现步骤
<!-- 详细描述复现问题的步骤 -->
1. 打开 '...'
2. 点击 '...'
3. 滚动到 '...'
4. 看到错误

## 🤔 预期行为
<!-- 简要描述预期的正确行为 -->

## 😯 截图
<!-- 如果适用，添加问题的截图 -->

## 🖥️ 环境信息
- 操作系统: [例如 Windows 10]
- 项目版本: [例如 1.0.0]
- Python版本: [例如 3.9.13]
- Nodejs版本: [例如 v20.14.0]

## 📋 其他信息
<!-- 在此添加关于此问题的任何其他上下文信息 -->
